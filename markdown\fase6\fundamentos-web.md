# 6.1 - Fundamentos Web

## O que é uma Aplicação Web?
- **Definição**: Sistema acessado via navegador, usando HTTP/HTTPS.
- **Exemplos**: Sites, sistemas de e-commerce, APIs REST.

## HTTP/S e Componentes
- **HTTP**: Protocolo de comunicação sem estado.
- **HTTPS**: HTTP com camada de criptografia (TLS/SSL).
- **Headers**: Informações de controle (User-Agent, Cookie, Set-Cookie, Authorization).
- **Sessões**: Identificação do usuário entre requisições.
- **Cookies**: Armazenamento de dados no navegador.

## APIs REST
- **REST**: Arquitetura para comunicação entre sistemas.
- **Métodos**: GET, POST, PUT, DELETE, PATCH.
- **Exemplo**:
```http
GET /api/usuarios HTTP/1.1
Host: exemplo.com
Authorization: Bearer token
```

## WAF (Web Application Firewall)
- **Definição**: Firewall especializado em proteger aplicações web.
- **Funções**: Bloqueio de ataques comuns (SQLi, XSS), análise de tráfego, regras customizadas.

## Boas Práticas de Segurança Web
- Usar HTTPS sempre.
- Validar e sanitizar entradas do usuário.
- Implementar controle de sessão seguro.
- Configurar headers de segurança (CSP, X-Frame-Options, HSTS).
- Limitar exposição de informações sensíveis.

## Conclusão
Compreender os fundamentos web é essencial para proteger aplicações e identificar riscos comuns. 