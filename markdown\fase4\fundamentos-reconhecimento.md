# 4.1 - Fundamentos de Reconhecimento

## Introdução ao Reconhecimento

### Definição e Objetivos
- **Reconhecimento**: Processo de coleta de informações sobre um alvo
- **Objetivos**:
  - Identificar pontos de entrada potenciais
  - Mapear infraestrutura e sistemas
  - Compreender a postura de segurança
  - Coletar informações para análise de riscos

### Tipos de Reconhecimento

#### Reconhecimento Passivo
- **Definição**: Coleta de informações sem interação direta com o alvo
- **Características**:
  - Não detectável pelo alvo
  - Baseado em informações públicas
  - Legal e ético
- **Exemplos**: OSINT, análise de DNS, pesquisa em redes sociais

#### Reconhecimento Ativo
- **Definição**: Interação direta com sistemas do alvo
- **Características**:
  - Pode ser detectado
  - Requer autorização
  - Mais invasivo
- **Exemplos**: Port scanning, banner grabbing, enumeração de serviços

## Footprinting

### Conceito de Footprinting
- **Definição**: Processo de criação de perfil de segurança de uma organização
- **Objetivos**:
  - Identificar domínios e subdomínios
  - Mapear endereços IP
  - Descobrir tecnologias utilizadas
  - Identificar funcionários e estrutura organizacional

### Informações Coletadas

#### Informações de Domínio
```bash
# Informações básicas de domínio
whois exemplo.com

# Informações de DNS
nslookup exemplo.com
dig exemplo.com

# Transferência de zona (se permitida)
dig @ns1.exemplo.com exemplo.com AXFR
```

#### Informações de Rede
```bash
# Mapeamento de IPs
nslookup exemplo.com
dig exemplo.com +short

# Range de IPs
whois -h whois.arin.net "n ***********/24"

# Informações de ASN
whois -h whois.radb.net "!gAS12345"
```

### Ferramentas de Footprinting

#### Whois
```bash
# Consulta básica
whois exemplo.com

# Consulta específica
whois -h whois.verisign-grs.com exemplo.com

# Informações de IP
whois ***********
```

#### DNS Tools
```bash
# Consulta DNS
dig exemplo.com
dig exemplo.com A
dig exemplo.com MX
dig exemplo.com NS

# Consulta reversa
dig -x ***********

# Zone transfer
dig @ns1.exemplo.com exemplo.com AXFR
```

#### Nslookup
```bash
# Modo interativo
nslookup
> set type=A
> exemplo.com
> set type=MX
> exemplo.com
> exit

# Modo não-interativo
nslookup exemplo.com
nslookup -type=MX exemplo.com
```

## OSINT (Open Source Intelligence)

### Definição e Escopo
- **OSINT**: Coleta de informações de fontes abertas
- **Fontes**:
  - Websites e redes sociais
  - Registros públicos
  - Fóruns e blogs
  - Documentos públicos
  - APIs públicas

### Técnicas de OSINT

#### Pesquisa em Redes Sociais
```bash
# Ferramentas de pesquisa
# theHarvester
theHarvester -d exemplo.com -b google

# Recon-ng
recon-ng
> use recon/profiles-profiles/namechk
> set SOURCE exemplo.com
> run

# Maltego
# Interface gráfica para mapeamento de relações
```

#### Pesquisa de Documentos
```bash
# Google Dorks
site:exemplo.com filetype:pdf
site:exemplo.com "password"
site:exemplo.com "admin"

# Shodan
# Pesquisa de dispositivos conectados à internet
shodan search hostname:exemplo.com
shodan search ssl:"exemplo.com"
```

#### Pesquisa de Emails
```bash
# theHarvester para emails
theHarvester -d exemplo.com -b all

# Hunter.io (API)
curl "https://api.hunter.io/v2/domain-search?domain=exemplo.com&api_key=YOUR_API_KEY"

# Email formatos
# Padrões comuns: <EMAIL>, <EMAIL>
```

### Ferramentas OSINT

#### theHarvester
```bash
# Instalação
pip install theHarvester

# Uso básico
theHarvester -d exemplo.com -b google
theHarvester -d exemplo.com -b all

# Opções avançadas
theHarvester -d exemplo.com -b google -l 500 -s 100
```

#### Recon-ng
```bash
# Instalação
git clone https://github.com/lanmaster53/recon-ng.git
cd recon-ng
pip install -r REQUIREMENTS

# Uso
recon-ng
> use recon/profiles-profiles/namechk
> set SOURCE exemplo.com
> run
```

#### Shodan
```bash
# CLI
pip install shodan
shodan init YOUR_API_KEY

# Pesquisas
shodan search hostname:exemplo.com
shodan search ssl:"exemplo.com"
shodan search org:"Empresa"
```

## Ética e Legalidade

### Princípios Éticos
- **Autorização**: Sempre obter permissão antes de realizar reconhecimento
- **Transparência**: Ser claro sobre objetivos e métodos
- **Proporcionalidade**: Usar apenas técnicas necessárias
- **Responsabilidade**: Aceitar consequências das ações

### Aspectos Legais
- **Jurisdição**: Conhecer leis locais e internacionais
- **Autorização**: Documentar permissões obtidas
- **Escopo**: Respeitar limites definidos
- **Confidencialidade**: Proteger informações coletadas

### Boas Práticas
```bash
# Documentar autorização
echo "Autorização obtida em: $(date)" > autorizacao.txt
echo "Escopo: exemplo.com e subdomínios" >> autorizacao.txt
echo "Responsável: Nome do profissional" >> autorizacao.txt

# Definir escopo claro
SCOPE="exemplo.com"
SUBDOMAINS="*.exemplo.com"
IP_RANGE="***********/24"

# Registrar atividades
echo "$(date): Iniciando reconhecimento de $SCOPE" >> log.txt
```

## Metodologia de Reconhecimento

### Fase 1: Planejamento
```bash
# Definir objetivos
OBJETIVOS=(
    "Identificar domínios e subdomínios"
    "Mapear endereços IP"
    "Descobrir tecnologias utilizadas"
    "Identificar funcionários"
)

# Definir escopo
SCOPE="exemplo.com"
TIMEFRAME="2024-01-01 a 2024-01-31"

# Documentar metodologia
cat > metodologia.txt << EOF
Metodologia de Reconhecimento
============================
Alvo: $SCOPE
Período: $TIMEFRAME
Técnicas: Passivas e ativas (com autorização)
Ferramentas: theHarvester, Recon-ng, Shodan
EOF
```

### Fase 2: Coleta Passiva
```bash
# Coletar informações de domínio
echo "=== Informações de Domínio ===" > relatorio.txt
whois $SCOPE >> relatorio.txt

# Enumerar subdomínios
echo "=== Subdomínios ===" >> relatorio.txt
theHarvester -d $SCOPE -b all >> relatorio.txt

# Pesquisar em Shodan
echo "=== Dispositivos na Internet ===" >> relatorio.txt
shodan search hostname:$SCOPE >> relatorio.txt
```

### Fase 3: Análise e Correlação
```bash
# Correlacionar informações
echo "=== Correlação de Dados ===" >> relatorio.txt

# Identificar padrões
grep -i "admin\|login\|portal" relatorio.txt
grep -i "vulnerabilidade\|exploit" relatorio.txt

# Mapear infraestrutura
echo "=== Mapeamento de Infraestrutura ===" >> relatorio.txt
```

### Fase 4: Relatório
```bash
# Gerar relatório final
cat > relatorio_final.md << EOF
# Relatório de Reconhecimento

## Resumo Executivo
- Alvo: $SCOPE
- Período: $TIMEFRAME
- Técnicas utilizadas: Passivas

## Descobertas Principais
$(cat relatorio.txt | grep -i "encontrado\|descoberto")

## Recomendações
1. Implementar monitoramento de subdomínios
2. Revisar configurações de DNS
3. Implementar detecção de vazamentos de dados

## Anexos
- Logs completos
- Capturas de tela
- Dados brutos coletados
EOF
```

## Ferramentas e Técnicas Avançadas

### Enumeração de Subdomínios
```bash
# Sublist3r
git clone https://github.com/aboul3la/Sublist3r.git
cd Sublist3r
python sublist3r.py -d exemplo.com

# Amass
amass enum -d exemplo.com

# Subfinder
subfinder -d exemplo.com -o subdomains.txt
```

### Análise de Certificados SSL
```bash
# Certificados SSL
curl -s "https://exemplo.com" | openssl x509 -text

# Censys
# Pesquisa de certificados SSL
censys search "parsed.subject_dn:/CN=exemplo.com"

# Crt.sh
# Certificados SSL públicos
curl -s "https://crt.sh/?q=%.exemplo.com&output=json" | jq
```

### Pesquisa de Vazamentos
```bash
# HaveIBeenPwned
# Verificar se email foi comprometido
curl -s "https://haveibeenpwned.com/api/v3/breachedaccount/<EMAIL>"

# DeHashed
# Pesquisa de credenciais vazadas
# Requer API key

# BreachDirectory
# Pesquisa local de vazamentos
```

## Monitoramento Contínuo

### Configuração de Alertas
```bash
# Script de monitoramento
cat > monitor_subdomains.sh << 'EOF'
#!/bin/bash
DOMAIN="exemplo.com"
PREVIOUS_FILE="previous_subdomains.txt"
CURRENT_FILE="current_subdomains.txt"

# Coletar subdomínios atuais
subfinder -d $DOMAIN -o $CURRENT_FILE

# Comparar com versão anterior
if [ -f $PREVIOUS_FILE ]; then
    echo "Novos subdomínios encontrados:"
    comm -13 $PREVIOUS_FILE $CURRENT_FILE
fi

# Atualizar arquivo anterior
cp $CURRENT_FILE $PREVIOUS_FILE
EOF

chmod +x monitor_subdomains.sh
```

### Automação de Relatórios
```bash
# Script de geração de relatório
cat > generate_report.sh << 'EOF'
#!/bin/bash
DOMAIN="exemplo.com"
DATE=$(date +%Y-%m-%d)

# Gerar relatório
echo "# Relatório de Reconhecimento - $DATE" > relatorio_$DATE.md
echo "## Domínio: $DOMAIN" >> relatorio_$DATE.md
echo "## Data: $DATE" >> relatorio_$DATE.md

# Adicionar informações coletadas
echo "### Subdomínios" >> relatorio_$DATE.md
cat subdomains.txt >> relatorio_$DATE.md

echo "### IPs" >> relatorio_$DATE.md
cat ips.txt >> relatorio_$DATE.md
EOF

chmod +x generate_report.sh
```

## Conclusão

O reconhecimento fornece:

- **Visibilidade**: Compreensão da postura de segurança
- **Identificação de Riscos**: Descoberta de vulnerabilidades
- **Planejamento**: Base para testes de penetração
- **Monitoramento**: Detecção de mudanças na infraestrutura

Para profissionais de segurança:
- **Fundamento**: Base para todas as atividades ofensivas
- **Legalidade**: Técnicas legais e éticas
- **Eficiência**: Otimização de recursos
- **Continuidade**: Processo contínuo de melhoria

O reconhecimento é a base para todas as outras atividades de segurança, fornecendo a compreensão necessária para proteger adequadamente os ativos. 