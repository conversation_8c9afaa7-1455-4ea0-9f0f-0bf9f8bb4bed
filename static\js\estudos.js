// Estrutura de dados para o roadmap teórico de cibersegurança
const roadmapData = {
    "fase0": {
        "title": "Fase 0: Introdução à Cibersegurança",
        "description": "Contextualizar a cibersegurança e sua relevância, preparando a base para o estudo técnico.",
        "subfases": {
            "0.1": {
                "title": "0.1 - Introdução à Cibersegurança",
                "description": "O que é cibersegurança, incidentes reais, papéis na área e estrutura do roadmap.",
                "file": "markdown/fase0/introducao-ciberseguranca.md"
            }
        }
    },
    "fase1": {
        "title": "Fase 1: Segurança da Informação e Governança",
        "description": "Entender os pilares teóricos da cibersegurança e sua gestão organizacional.",
        "subfases": {
            "1.1": {
                "title": "1.1 - Conceitos Fundamentais",
                "description": "Tríade CIA, princípios AAA, ameaças, riscos, vulnerabilidades.",
                "file": "markdown/fase1/conceitos-fundamentais.md"
            },
            "1.2": {
                "title": "1.2 - Governança, Risco e Compliance (GRC)",
                "description": "Frameworks NIST, ISO/IEC 27001, OWASP, regulamentações GDPR/LGPD.",
                "file": "markdown/fase1/grc.md"
            },
            "1.3": {
                "title": "1.3 - Times de Segurança",
                "description": "Red Team, Blue Team, Purple Team, Yellow Team.",
                "file": "markdown/fase1/times-seguranca.md"
            }
        }
    },
    "fase2": {
        "title": "Fase 2: Fundamentos de Sistemas e Redes",
        "description": "Compreender o funcionamento de computadores, redes, criptografia básica e ambientes virtuais.",
        "subfases": {
            "2.1": {
                "title": "2.1 - Fundamentos de Sistemas Operacionais",
                "description": "Linux e Windows, comandos básicos, estrutura de arquivos.",
                "file": "markdown/fase2/fundamentos-so.md"
            },
            "2.2": {
                "title": "2.2 - Fundamentos de Redes",
                "description": "Protocolos TCP/IP, DNS, DHCP, roteamento básico.",
                "file": "markdown/fase2/fundamentos-redes.md"
            },
            "2.3": {
                "title": "2.3 - Criptografia Básica",
                "description": "Criptografia simétrica e assimétrica, hashing, certificados.",
                "file": "markdown/fase2/criptografia-basica.md"
            },
            "2.4": {
                "title": "2.4 - Virtualização",
                "description": "VMs, containers, Docker, ambientes isolados.",
                "file": "markdown/fase2/virtualizacao.md"
            }
        }
    },
    "fase3": {
        "title": "Fase 3: Fundamentos de Programação",
        "description": "Compreender a lógica de programação e sua aplicação teórica em cibersegurança.",
        "subfases": {
            "3.1": {
                "title": "3.1 - Lógica e Python",
                "description": "Tipos de dados, estruturas de controle, bibliotecas para segurança.",
                "file": "markdown/fase3/logica-python.md"
            },
            "3.2": {
                "title": "3.2 - Bash e PowerShell",
                "description": "Scripts, automação, variáveis, loops, condicionais.",
                "file": "markdown/fase3/bash-powershell.md"
            },
            "3.3": {
                "title": "3.3 - Controle de Versões com Git",
                "description": "Git e GitHub para versionamento de scripts de segurança.",
                "file": "markdown/fase3/git-versionamento.md"
            }
        }
    },
    "fase4": {
        "title": "Fase 4: Reconhecimento e OSINT",
        "description": "Aprender a coletar informações públicas de forma ética e teórica.",
        "subfases": {
            "4.1": {
                "title": "4.1 - Fundamentos de Reconhecimento",
                "description": "Footprinting, ética em OSINT, fontes públicas.",
                "file": "markdown/fase4/fundamentos-reconhecimento.md"
            },
            "4.2": {
                "title": "4.2 - Técnicas e Ferramentas (Teoria)",
                "description": "Google Dorking, Whois, DNS, Shodan, TheHarvester, Maltego.",
                "file": "markdown/fase4/tecnicas-ferramentas.md"
            }
        }
    },
    "fase5": {
        "title": "Fase 5: Análise de Vulnerabilidades",
        "description": "Compreender como vulnerabilidades são identificadas, classificadas e gerenciadas.",
        "subfases": {
            "5.1": {
                "title": "5.1 - Conceitos de Vulnerabilidades",
                "description": "CVE, CVSS, CWE, ciclo de vida, gestão de patches.",
                "file": "markdown/fase5/conceitos-vulnerabilidades.md"
            },
            "5.2": {
                "title": "5.2 - Scanners (Teoria)",
                "description": "Nmap, OpenVAS, Nessus, scanners em nuvem, falsos positivos.",
                "file": "markdown/fase5/scanners.md"
            }
        }
    },
    "fase6": {
        "title": "Fase 6: Segurança de Aplicações Web",
        "description": "Entender o funcionamento e a segurança de aplicações web.",
        "subfases": {
            "6.1": {
                "title": "6.1 - Fundamentos Web",
                "description": "HTTP/S, headers, sessões, cookies, APIs REST, WAF.",
                "file": "markdown/fase6/fundamentos-web.md"
            },
            "6.2": {
                "title": "6.2 - OWASP Top 10",
                "description": "Injeções, Broken Authentication, XSS, Security Misconfiguration.",
                "file": "markdown/fase6/owasp-top10.md"
            }
        }
    },
    "fase7": {
        "title": "Fase 7: Segurança de Infraestrutura",
        "description": "Proteger redes, sistemas e ambientes corporativos.",
        "subfases": {
            "7.1": {
                "title": "7.1 - Segurança de Redes",
                "description": "VLANs, firewalls, IDS/IPS, ataques MITM, ARP spoofing.",
                "file": "markdown/fase7/seguranca-redes.md"
            },
            "7.2": {
                "title": "7.2 - Segurança em Linux e Windows",
                "description": "Hardening, logs, auditoria, Active Directory, GPOs.",
                "file": "markdown/fase7/seguranca-linux-windows.md"
            },
            "7.3": {
                "title": "7.3 - Gestão de Identidade e Acesso (IAM)",
                "description": "Menor privilégio, segregação de funções, SSO, MFA, PAM.",
                "file": "markdown/fase7/iam.md"
            }
        }
    },
    "fase8": {
        "title": "Fase 8: Segurança em Nuvem",
        "description": "Compreender riscos e controles em ambientes de nuvem.",
        "subfases": {
            "8.1": {
                "title": "8.1 - Fundamentos Cloud",
                "description": "IaaS, PaaS, SaaS, Shared Responsibility Model, compliance.",
                "file": "markdown/fase8/fundamentos-cloud.md"
            },
            "8.2": {
                "title": "8.2 - Segurança em AWS, Azure e GCP",
                "description": "IAM, S3, VPC, Security Groups, Azure AD, GCP Security.",
                "file": "markdown/fase8/aws-azure-gcp.md"
            }
        }
    },
    "fase9": {
        "title": "Fase 9: Fundamentos de Defesa e Detecção",
        "description": "Estudar monitoramento, detecção e resposta a ameaças.",
        "subfases": {
            "9.1": {
                "title": "9.1 - SIEM e SOC",
                "description": "Security Operations Center, logs, correlação, alertas, SOAR.",
                "file": "markdown/fase9/siem-soc.md"
            },
            "9.2": {
                "title": "9.2 - Análise de Ameaças e Resposta a Incidentes",
                "description": "Threat Hunting, MITRE ATT&CK, IOCs, playbooks de resposta.",
                "file": "markdown/fase9/analise-ameacas.md"
            }
        }
    },
    "fase10": {
        "title": "Fase 10: Especializações Avançadas",
        "description": "Aprofundar em tópicos avançados de cibersegurança.",
        "subfases": {
            "10.1": {
                "title": "10.1 - Criptografia Aplicada",
                "description": "Simétrica vs. assimétrica, hashing, assinaturas digitais, TLS/SSL.",
                "file": "markdown/fase10/criptografia-aplicada.md"
            },
            "10.2": {
                "title": "10.2 - Análise de Malware",
                "description": "Estática vs. dinâmica, assinaturas, ransomware, fileless malware.",
                "file": "markdown/fase10/analise-malware.md"
            },
            "10.3": {
                "title": "10.3 - Forense Digital",
                "description": "Coleta de evidências, cadeia de custódia, análise de sistemas.",
                "file": "markdown/fase10/forense-digital.md"
            },
            "10.4": {
                "title": "10.4 - Segurança Mobile e IoT",
                "description": "Android, iOS, dispositivos IoT, análise de APKs, sandboxing.",
                "file": "markdown/fase10/seguranca-mobile-iot.md"
            },
            "10.5": {
                "title": "10.5 - Exploit Development",
                "description": "Assembly básico, buffer/heap overflows, ROP, format string bugs.",
                "file": "markdown/fase10/exploit-development.md"
            },
            "10.6": {
                "title": "10.6 - Threat Intelligence",
                "description": "Fontes, plataformas, TTPs, MITRE ATT&CK, classificação de ameaças.",
                "file": "markdown/fase10/threat-intelligence.md"
            }
        }
    },
    "fase11": {
        "title": "Fase 11: Certificações e Carreira",
        "description": "Preparar-se para atuar no mercado de cibersegurança.",
        "subfases": {
            "11.1": {
                "title": "11.1 - Certificações Profissionais",
                "description": "CompTIA Security+, CEH, CISSP, OSCP, CCSP, comparação de focos.",
                "file": "markdown/fase11/certificacoes-profissionais.md"
            },
            "11.2": {
                "title": "11.2 - Carreira em Segurança",
                "description": "Red Team, Blue Team, GRC, analista SOC, soft skills, portfólio.",
                "file": "markdown/fase11/carreira-seguranca.md"
            }
        }
    }
};

// Função para gerar a navegação da sidebar
function generateSidebarNavigation() {
        const sidebarNav = document.getElementById('sidebar-nav');
        if (!sidebarNav) return;

        sidebarNav.innerHTML = '';

    Object.keys(roadmapData).forEach(phaseKey => {
        const phase = roadmapData[phaseKey];
        
        // Criar container da fase
        const phaseContainer = document.createElement('div');
        phaseContainer.className = 'phase-container';
        
        // Criar botão da fase
        const phaseButton = document.createElement('button');
        phaseButton.className = 'phase-button';
        
        phaseButton.innerHTML = `
            <div class="phase-header">
                <div class="phase-number">${phaseKey.replace('fase', '')}</div>
                <div class="phase-info">
                    <div class="phase-title">${phase.title}</div>
                    <div class="phase-description">${phase.description}</div>
                </div>
                <div class="phase-chevron">
                    <i data-lucide="chevron-down"></i>
            </div>
            </div>
        `;
        
        // Criar container das subfases
        const subfasesContainer = document.createElement('div');
        subfasesContainer.className = 'subfases-container hidden';
        
        // Adicionar subfases
        Object.keys(phase.subfases).forEach(subfaseKey => {
            const subfase = phase.subfases[subfaseKey];
            
            const subfaseButton = document.createElement('button');
            subfaseButton.className = 'subfase-button';
            
            subfaseButton.innerHTML = `
                <div class="subfase-content">
                    <div class="subfase-icon">
                        <i data-lucide="book-open"></i>
                    </div>
                    <div class="subfase-info">
                        <div class="subfase-title">${subfase.title}</div>
                        <div class="subfase-description">${subfase.description}</div>
                    </div>
            </div>
            `;
            
            subfaseButton.addEventListener('click', (e) => {
                e.stopPropagation(); // Evita que o clique propague para a fase
                loadContent(subfase.file, subfase.title, `${phase.title} > ${subfase.title}`);
            });
            
            subfasesContainer.appendChild(subfaseButton);
        });
        
        // Toggle da fase - apenas expande/contrai subfases
        phaseButton.addEventListener('click', () => {
            const isExpanded = subfasesContainer.classList.contains('hidden');
            subfasesContainer.classList.toggle('hidden');
            
            const chevron = phaseButton.querySelector('[data-lucide="chevron-down"]');
            if (isExpanded) {
                chevron.style.transform = 'rotate(180deg)';
            } else {
                chevron.style.transform = 'rotate(0deg)';
            }
        });
        
        phaseContainer.appendChild(phaseButton);
        phaseContainer.appendChild(subfasesContainer);
        sidebarNav.appendChild(phaseContainer);
    });
}

// Função para carregar conteúdo
function loadContent(filePath, title, path) {
    const contentArea = document.getElementById('content-area');
    const dashboardArea = document.getElementById('dashboard-area');
    const contentTitle = document.getElementById('content-title');
    const contentPath = document.getElementById('content-path');
    const markdownContent = document.getElementById('markdown-content');
    
    if (!contentArea || !dashboardArea || !contentTitle || !contentPath || !markdownContent) return;
    
    // Mostrar área de conteúdo e esconder dashboard
    contentArea.classList.remove('hidden');
    dashboardArea.classList.add('hidden');
    
    // Atualizar título e caminho
    contentTitle.textContent = title;
    contentPath.textContent = path;
    
    // Construir URL para a API do Flask
    const apiUrl = `/markdown/${filePath.replace('markdown/', '')}`;
    
    // Carregar conteúdo markdown via API
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            // Usar o HTML já convertido pelo Flask
            markdownContent.innerHTML = data.content;
        })
        .catch(error => {
            console.error('Erro ao carregar conteúdo:', error);
            markdownContent.innerHTML = `
                <div class="text-center text-muted">
                    <i data-lucide="alert-circle" class="w-12 h-12 mx-auto mb-4"></i>
                    <h3 class="text-lg font-semibold mb-2">Conteúdo não encontrado</h3>
                    <p>O arquivo de conteúdo não foi encontrado ou está sendo preparado.</p>
                    <p class="text-sm mt-2">Erro: ${error.message}</p>
                </div>
            `;
        });
}

// Função para carregar o markdown do dashboard
function loadDashboardMarkdown() {
    const dashboardMarkdownContent = document.getElementById('dashboard-markdown-content');
    if (!dashboardMarkdownContent) return;
    
    fetch('/markdown/dashboard.md')
        .then(response => response.json())
        .then(data => {
            if (data.error) throw new Error(data.error);
            dashboardMarkdownContent.innerHTML = data.content;
        })
        .catch(error => {
            console.error('Erro ao carregar dashboard:', error);
            dashboardMarkdownContent.innerHTML = `
                <div class="text-center text-muted">
                    <i data-lucide="alert-circle" class="w-12 h-12 mx-auto mb-4"></i>
                    <h3 class="text-lg font-semibold mb-2">Erro ao carregar dashboard</h3>
                    <p>Não foi possível carregar o conteúdo do dashboard.</p>
                    <p class="text-sm mt-2">Erro: ${error.message}</p>
                </div>
            `;
        });
}

// Função utilitária para atualizar a breadcrumb com separadores estilizados
function setBreadcrumb(items) {
  const el = document.getElementById('content-path');
  if (!el) return;
  el.innerHTML = items.map((item, idx) => idx === 0 ? `<span>${item}</span>` : `<span class='breadcrumb-sep'>&gt;</span><span>${item}</span>`).join('');
}


// Inicializar quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    generateSidebarNavigation();
    loadDashboardMarkdown();
    
    // Botão voltar ao dashboard
    const backButton = document.getElementById('back-to-dashboard');
    if (backButton) {
        backButton.addEventListener('click', () => {
            const contentArea = document.getElementById('content-area');
            const dashboardArea = document.getElementById('dashboard-area');
            
            if (contentArea && dashboardArea) {
                contentArea.classList.add('hidden');
                dashboardArea.classList.remove('hidden');
            }
        });
    }

    // --- Sidebar toggle ---
    const sidebar = document.getElementById('study-sidebar');
    const toggleSidebarBtn = document.getElementById('toggle-sidebar');
    const showSidebarBtn = document.getElementById('show-sidebar-btn');
    if (toggleSidebarBtn && sidebar && showSidebarBtn) {
        toggleSidebarBtn.addEventListener('click', () => {
            sidebar.style.display = 'none';
            showSidebarBtn.classList.remove('hidden');
        });
        showSidebarBtn.querySelector('button').addEventListener('click', () => {
            sidebar.style.display = '';
            showSidebarBtn.classList.add('hidden');
        });
    }

    // Atualizar tempo da sessão
    setInterval(() => {
        const sessionTime = document.getElementById('session-time');
        if (sessionTime) {
            // Implementar lógica de tempo de sessão
            sessionTime.textContent = 'Session: 00:00:00';
        }
    }, 1000);
});
