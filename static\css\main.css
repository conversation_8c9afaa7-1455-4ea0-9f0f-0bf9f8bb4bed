/* CSS Reset e Base */
@import url('https://fonts.googleapis.com/css?family=Poppins:400,500,600,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Variables - Professional Dark Theme */
:root {
    --radius: 0.5rem;

    /* Core Colors - Monochrome */
    --background: #000000;
    --foreground: #ffffff;
    --card: #111111;
    --card-foreground: #ffffff;
    --popover: #1a1a1a;
    --popover-foreground: #ffffff;

    /* Primary Colors - White/Light Gray */
    --primary: #ffffff;
    --primary-foreground: #000000;
    --primary-hover: #f5f5f5;

    /* Secondary Colors - Dark Gray */
    --secondary: #1a1a1a;
    --secondary-foreground: #e5e5e5;
    --secondary-hover: #2a2a2a;

    /* Muted Colors - Medium Gray */
    --muted: #2a2a2a;
    --muted-foreground: #a1a1a1;

    /* Accent Colors - Dark Blue (minimal use) */
    --accent: #222;
    --accent-foreground: #ffffff;
    --accent-hover: #444;
    --accent-light: #888;

    /* Status Colors - Grayscale */
    --success: #ffffff;
    --warning: #d1d5db;
    --destructive: #6b7280;

    /* Borders - Gray tones */
    --border: #333333;
    --border-light: #404040;
    --border-accent: #222;
    --input: #1a1a1a;
    --ring: #222;

    /* Sidebar - Darker theme */
    --sidebar: #0a0a0a;
    --sidebar-foreground: #ffffff;
    --sidebar-border: #1a1a1a;

    /* Shadows - Subtle */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.8);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.6);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.5);

    /* Gradients - Monochrome */
    --gradient-primary: linear-gradient(135deg, #ffffff, #e5e5e5);
    --gradient-dark: linear-gradient(135deg, #1a1a1a, #000000);
    --gradient-card: linear-gradient(135deg, #111111, #0a0a0a);
    --gradient-accent: linear-gradient(135deg, #222, #000);

    /* Fonts */
    --font-geist-sans: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-geist-mono: 'Fira Code', 'Geist Mono', 'Consolas', monospace;
}

/* Base Styles */
html {
    font-family: var(--font-geist-sans);
    line-height: 1.6;
    scroll-behavior: smooth;
}

body {
    background: var(--background);
    color: var(--foreground);
    font-family: var(--font-geist-sans);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background);
}

::-webkit-scrollbar-thumb {
    background: var(--muted);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary);
    box-shadow: var(--glow-primary);
}

/* Typography */
.font-mono {
    font-family: var(--font-geist-mono);
}

.font-bold {
    font-weight: 700;
}

.font-semibold {
    font-weight: 600;
}

.font-medium {
    font-weight: 500;
}

/* Text Sizes */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }

/* Colors */
.text-white { color: var(--foreground); }
.text-black { color: var(--background); }
.text-primary { color: var(--primary); }
.text-accent { color: var(--accent); }
.text-muted { color: var(--muted-foreground); }
.text-secondary { color: var(--secondary-foreground); }

.bg-primary { background-color: var(--primary); }
.bg-secondary { background-color: var(--secondary); }
.bg-card { background-color: var(--card); }
.bg-muted { background-color: var(--muted); }

/* Professional Effects */
.gradient-primary {
    background: var(--gradient-primary);
}

.gradient-card {
    background: var(--gradient-card);
}

.gradient-accent {
    background: var(--gradient-accent);
}

/* Layout */
.min-h-screen { min-height: 100vh; }
.h-screen { height: 100vh; }
.w-full { width: 100%; }
.flex { display: flex; }
.grid { display: grid; }
.hidden {
  display: none !important;
}
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }

/* Flexbox */
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-items-center { justify-items: center; }
.flex-1 { flex: 1 1 0%; }

/* Grid */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-rows-3 { grid-template-rows: repeat(3, minmax(0, 1fr)); }
.row-start-2 { grid-row-start: 2; }
.row-start-3 { grid-row-start: 3; }

/* Spacing */
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }
.py-0\.5 { padding-top: 0.125rem; padding-bottom: 0.125rem; }
.pl-4 { padding-left: 1rem; }
.pl-6 { padding-left: 1.5rem; }
.pb-20 { padding-bottom: 5rem; }

.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-8 { margin-bottom: 2rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-auto { margin-top: auto; }
.ml-4 { margin-left: 1rem; }
.ml-6 { margin-left: 1.5rem; }
.mr-1 { margin-right: 0.25rem; }

/* Gaps */
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-16 { gap: 4rem; }

/* Borders */
.border { border-width: 1px; }
.border-b { border-bottom-width: 1px; }
.border-r { border-right-width: 1px; }
.border-l { border-left-width: 1px; }
.border-t { border-top-width: 1px; }
.border-gray-700 { border-color: #374151; }
.border-gray-800 { border-color: #1f2937; }
.border-solid { border-style: solid; }
.border-transparent { border-color: transparent; }

/* Border Radius */
.rounded { border-radius: 0.25rem; }
.rounded-full { border-radius: 9999px; }

/* Sizing */
.w-1 { width: 0.25rem; }
.w-2 { width: 0.5rem; }
.w-3 { width: 0.75rem; }
.w-4 { width: 1rem; }
.w-6 { width: 1.5rem; }
.w-16 { width: 4rem; }
.w-20 { width: 5rem; }
.w-80 { width: 20rem; }

.h-1 { height: 0.25rem; }
.h-2 { height: 0.5rem; }
.h-3 { height: 0.75rem; }
.h-4 { height: 1rem; }
.h-6 { height: 1.5rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-auto { height: auto; }

/* Positioning */
.relative { position: relative; }
.absolute { position: absolute; }
.top-2 { top: 0.5rem; }
.right-2 { right: 0.5rem; }
.z-10 { z-index: 10; }
.z-50 { z-index: 50; }

/* Overflow */
.overflow-y-auto { overflow-y: auto; }

/* Opacity */
.opacity-60 { opacity: 0.6; }

/* Transitions */
.transition-all { transition: all 0.15s ease-in-out; }
.transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out; }

/* Hover States */
.hover\:bg-gray-800:hover { background-color: #1f2937; }
.hover\:bg-gray-800\/40:hover { background-color: rgba(31, 41, 55, 0.4); }
.hover\:text-white:hover { color: #ffffff; }
.hover\:underline:hover { text-decoration: underline; }
.hover\:underline-offset-4:hover { text-underline-offset: 4px; }
.hover\:border-transparent:hover { border-color: transparent; }

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: .5; }
}

.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }

/* Effects */
.backdrop-blur-sm { backdrop-filter: blur(4px); }

/* Responsive Design */
@media (max-width: 768px) {
    .mobile-stack {
        flex-direction: column;
    }

    .mobile-full {
        width: 100%;
    }

    .mobile-text-center {
        text-align: center;
    }

    .mobile-p-4 {
        padding: 1rem;
    }
}

@media (min-width: 640px) {
    .sm\:p-20 { padding: 5rem; }
    .sm\:text-left { text-align: left; }
    .sm\:text-base { font-size: 1rem; line-height: 1.5rem; }
    .sm\:h-12 { height: 3rem; }
    .sm\:px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }
    .sm\:w-auto { width: auto; }
    .sm\:flex-row { flex-direction: row; }
    .sm\:items-start { align-items: flex-start; }
}

@media (min-width: 768px) {
    .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .md\:w-\[158px\] { width: 158px; }
}

@media (prefers-color-scheme: dark) {
    :root {
        --background: #000000;
        --foreground: #ffffff;
    }
}

/* Text Alignment */
.text-center { text-align: center; }
.text-left { text-align: left; }

/* Max Width */
.max-w-4xl { max-width: 56rem; }

/* Flex Utilities */
.shrink-0 { flex-shrink: 0; }

/* Lists */
.list-inside { list-style-position: inside; }
.list-decimal { list-style-type: decimal; }

/* Typography */
.tracking-\[-\.01em\] { letter-spacing: -0.01em; }

/* Button Styles */
button {
    cursor: pointer;
    border: none;
    outline: none;
    background: none;
    font-family: inherit;
}

button:hover {
    opacity: 0.8;
}

button:active {
    transform: scale(0.98);
}

.btn-primary {
    background: var(--primary);
    color: var(--background);
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius);
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--secondary);
    color: var(--secondary-foreground);
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius);
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background: var(--secondary-hover);
    transform: translateY(-1px);
}

/* Card Styles */
.card-enhanced {
    background: var(--card);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.card-enhanced:hover {
    border-color: var(--primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Sidebar Navigation Styles */
.phase-container {
    margin-bottom: 1rem;
}

.phase-button {
    width: 100%;
    text-align: left;
    padding: 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    background: var(--card);
    border: 1px solid var(--border);
    cursor: pointer;
}

.phase-button:hover {
    background: var(--secondary-hover);
    border-color: var(--primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.phase-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.phase-number {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background: var(--primary);
    color: var(--background);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.phase-info {
    flex: 1;
    min-width: 0;
}

.phase-title {
    font-weight: 600;
    color: var(--primary);
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    line-height: 1.2;
}

.phase-description {
    color: var(--muted-foreground);
    font-size: 0.75rem;
    line-height: 1.3;
}

.phase-chevron {
    flex-shrink: 0;
    transition: transform 0.3s ease;
}

.phase-chevron i {
    width: 1rem;
    height: 1rem;
    color: var(--muted-foreground);
}

.subfases-container {
    margin-top: 0.5rem;
    padding-left: 1rem;
    border-left: 2px solid var(--border);
    margin-left: 1.25rem;
}

.subfases-container.hidden {
    display: none;
}

.subfase-button {
    width: 100%;
    text-align: left;
    padding: 0.75rem;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    background: var(--muted);
    border: 1px solid var(--border-light);
    margin-bottom: 0.25rem;
    cursor: pointer;
}

.subfase-button:hover {
    background: var(--secondary-hover);
    border-color: var(--primary);
    transform: translateX(2px);
}

.subfase-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.subfase-icon {
    flex-shrink: 0;
}

.subfase-icon i {
    width: 1rem;
    height: 1rem;
    color: var(--muted-foreground);
}

.subfase-info {
    flex: 1;
    min-width: 0;
}

.subfase-title {
    font-weight: 500;
    color: var(--secondary-foreground);
    font-size: 0.875rem;
    margin-bottom: 0.125rem;
    line-height: 1.2;
}

.subfase-description {
    color: var(--muted-foreground);
    font-size: 0.75rem;
    line-height: 1.3;
}

/* Sidebar Scrollbar */
#study-sidebar::-webkit-scrollbar {
    width: 6px;
}

#study-sidebar::-webkit-scrollbar-track {
    background: var(--sidebar);
}

#study-sidebar::-webkit-scrollbar-thumb {
    background: var(--muted);
    border-radius: 3px;
}

#study-sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--primary);
}

/* Prose Styles */
/* Enhanced Markdown Styling */
.prose {
    max-width: 100%;
    width: 100%;
    color: var(--foreground);
    font-family: 'Fira Code', 'Consolas', 'Monaco', 'Andale Mono', 'Ubuntu Mono', monospace;
    line-height: 1.7;
    font-size: 0.95rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Headings */
.prose h1 {
    color: var(--primary);
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 2rem;
    margin-top: 3rem;
    position: relative;
    padding-bottom: 1rem;
    border-bottom: 3px solid var(--accent);
    text-transform: uppercase;
    letter-spacing: -0.02em;
    font-family: 'Fira Code', 'Consolas', 'Monaco', 'Andale Mono', 'Ubuntu Mono', monospace;
}

.prose h1::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: -3px;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--accent), transparent);
}

.prose h2 {
    color: var(--primary);
    font-size: 2rem;
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    margin-top: 2.5rem;
    position: relative;
    padding-left: 1.5rem;
    border-left: 4px solid var(--accent);
    background: linear-gradient(90deg, #222, transparent);
    padding: 1rem 1.5rem;
    border-radius: 0 8px 8px 0;
    font-family: 'Fira Code', 'Consolas', 'Monaco', 'Andale Mono', 'Ubuntu Mono', monospace;
}

.prose h3 {
    color: var(--primary);
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
    margin-top: 2rem;
    position: relative;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--border);
    font-family: 'Fira Code', 'Consolas', 'Monaco', 'Andale Mono', 'Ubuntu Mono', monospace;
}

.prose h4 {
    color: var(--primary);
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 0.75rem;
    margin-top: 1.5rem;
    color: var(--secondary-foreground);
    font-family: 'Fira Code', 'Consolas', 'Monaco', 'Andale Mono', 'Ubuntu Mono', monospace;
}

/* Paragraphs */
.prose p {
    margin-bottom: 1.5rem;
    line-height: 1.6;
    color: var(--foreground);
    text-align: left;
    font-size: 0.95rem;
    font-weight: 400;
    word-wrap: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
}

/* Lists */
.prose ul, .prose ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.prose li {
    margin-bottom: 0.75rem;
    line-height: 1.5;
    color: var(--foreground);
    position: relative;
    font-size: 0.95rem;
    font-weight: 400;
}

.prose ul li::before {
    content: '▸';
    color: var(--accent);
    font-weight: bold;
    position: absolute;
    left: -1.5rem;
    font-size: 1.2em;
}

.prose ol {
    counter-reset: list-counter;
}

.prose ol li {
    counter-increment: list-counter;
    position: relative;
}

.prose ol li::before {
    content: counter(list-counter) '.';
    color: var(--accent);
    font-weight: bold;
    position: absolute;
    left: -2rem;
    min-width: 1.5rem;
}

/* Strong and Emphasis */
.prose strong {
    color: var(--primary);
    font-weight: 700;
    background: linear-gradient(120deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
    padding: 0.1rem 0.3rem;
    border-radius: 3px;
}

.prose em {
    color: var(--secondary-foreground);
    font-style: italic;
    font-weight: 500;
}

/* Code Blocks */
.prose code {
    background: var(--card);
    color: var(--accent-light);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.9em;
    font-family: var(--font-geist-mono);
    border: 1px solid var(--border);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.prose pre {
    background: var(--card);
    color: var(--foreground);
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 2rem 0;
    border: 1px solid var(--border);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    position: relative;
}

.prose pre::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent), var(--accent-light));
    border-radius: 8px 8px 0 0;
}

.prose pre code {
    background: transparent;
    padding: 0;
    border: none;
    color: inherit;
    font-size: 0.9em;
    line-height: 1.6;
}

/* Blockquotes */
.prose blockquote {
    border-left: 4px solid var(--accent);
    padding: 1.5rem 2rem;
    margin: 2rem 0;
    font-style: italic;
    color: var(--secondary-foreground);
    background: linear-gradient(90deg, #222, transparent);
    border-radius: 0 8px 8px 0;
    position: relative;
}

.prose blockquote::before {
    content: '"';
    position: absolute;
    top: 0.5rem;
    left: 1rem;
    font-size: 3rem;
    color: var(--accent);
    opacity: 0.3;
    font-family: serif;
}

/* Tables */
.prose table {
    width: 100%;
    border-collapse: collapse;
    margin: 2rem 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.prose th, .prose td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border);
}

.prose th {
    background: var(--muted);
    font-weight: 600;
    color: var(--primary);
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
}

.prose td {
    background: var(--card);
    color: var(--foreground);
}

.prose tr:hover td {
    background: var(--secondary);
}

/* Links */
.prose a {
    color: var(--accent-light);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: all 0.2s ease;
}

.prose a:hover {
    color: var(--accent);
    border-bottom-color: var(--accent);
}

/* Horizontal Rules */
.prose hr {
    border: none;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--accent), transparent);
    margin: 3rem 0;
}

/* Special Elements */
.prose .highlight {
    background: linear-gradient(120deg, #222, transparent);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    border-left: 4px solid var(--accent);
    margin: 1.5rem 0;
}

.prose .warning {
    background: linear-gradient(120deg, rgba(220, 38, 38, 0.1), transparent);
    padding: 1rem 1.5rem;
    border-radius: 6px;
    border-left: 4px solid #dc2626;
    margin: 1.5rem 0;
    color: #fca5a5;
}

.prose .info {
    background: linear-gradient(120deg, rgba(59, 130, 246, 0.1), transparent);
    padding: 1rem 1.5rem;
    border-radius: 6px;
    border-left: 4px solid #444;
    margin: 1.5rem 0;
    color: #ccc;
}

/* Responsive Design */
@media (max-width: 768px) {
    .prose {
        max-width: 100%;
        width: 100%;
        padding: 0 0.5rem;
    }
    
    .prose h1 {
        font-size: 1.75rem;
        margin-top: 2rem;
    }
    
    .prose h2 {
        font-size: 1.5rem;
        margin-top: 1.5rem;
    }
    
    .prose h3 {
        font-size: 1.25rem;
        margin-top: 1.25rem;
    }
    
    .prose p, .prose li {
        font-size: 0.9rem;
    }
    
    .prose pre {
        padding: 1rem;
        font-size: 0.875rem;
        overflow-x: auto;
    }
}

@media (min-width: 1200px) {
    .prose {
        max-width: 100%;
        padding: 0 2rem;
    }
}

/* Enhanced Markdown Animations */
.prose h1, .prose h2, .prose h3, .prose h4 {
    transition: all 0.3s ease;
}

.prose h1:hover, .prose h2:hover, .prose h3:hover, .prose h4:hover {
    transform: translateX(5px);
}

.prose pre {
    transition: all 0.3s ease;
}

.prose pre:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.prose blockquote {
    transition: all 0.3s ease;
}

.prose blockquote:hover {
    transform: translateX(5px);
    background: linear-gradient(90deg, #222, transparent);
}

.prose table {
    transition: all 0.3s ease;
}

.prose table:hover {
    transform: scale(1.01);
}

/* Code syntax highlighting improvements */
.prose code {
    transition: all 0.2s ease;
}

.prose code:hover {
    background: var(--muted);
    transform: scale(1.05);
}

/* List item hover effects */
.prose li {
    transition: all 0.2s ease;
}

.prose li:hover {
    transform: translateX(3px);
}

/* Link hover animations */
.prose a {
    position: relative;
    transition: all 0.3s ease;
}

.prose a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent);
    transition: width 0.3s ease;
}

.prose a:hover::after {
    width: 100%;
}

/* Enhanced focus states for accessibility */
.prose *:focus {
    outline: 2px solid var(--accent);
    outline-offset: 2px;
    border-radius: 4px;
}

/* Matrix Rain Effect */
.matrix-rain {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 0.1;
}

@keyframes matrix-scroll {
    0% { transform: translateY(-100%); }
    100% { transform: translateY(100vh); }
}

@keyframes matrix-pulse {
    0%, 100% { opacity: 0.1; }
    50% { opacity: 0.2; }
}

/* Terminal Card */
.terminal-card {
    background: var(--card);
    border: 1px solid var(--border);
    border-radius: 0.5rem;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.terminal-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), transparent);
    animation: pulse 2s ease-in-out infinite;
}

.terminal-card:hover {
    border-color: var(--primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.terminal-card:hover::before {
    background: linear-gradient(90deg, var(--primary), var(--accent));
}

/* Terminal Button */
.terminal-button {
    background: var(--card);
    color: var(--foreground);
    border: 1px solid var(--border);
    border-radius: 0.25rem;
    padding: 0.75rem 1rem;
    font-family: var(--font-geist-mono);
    font-size: 0.875rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.terminal-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, var(--primary), transparent);
    transition: left 0.5s ease;
}

.terminal-button:hover {
    border-color: var(--primary);
    color: var(--primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.terminal-button:hover::before {
    left: 100%;
}

/* Cypher Title */
.cypher-title {
    font-size: clamp(3rem, 8vw, 8rem);
    font-weight: 900;
    background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    margin-bottom: 2rem;
    animation: titleShimmer 3s ease-in-out infinite;
    position: relative;
    z-index: 1;
}

@keyframes titleShimmer {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.2); }
}

@keyframes titleFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* Prompt Symbol */
.prompt-symbol {
    font-size: 2rem;
    color: var(--primary);
    animation: promptPulse 2s ease-in-out infinite;
}

@keyframes promptPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Typing Cursor */
.typing-cursor {
    display: inline-block;
    width: 2px;
    height: 1.2em;
    background: var(--primary);
    animation: cursorBlink 1s infinite;
}

@keyframes cursorBlink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

@keyframes cursorGlow {
    0%, 100% { box-shadow: 0 0 5px var(--primary); }
    50% { box-shadow: 0 0 20px var(--primary), 0 0 30px var(--primary); }
}

/* Title Container Hover Effects */
.title-container:hover .cypher-title {
    animation: titleFloat 2s ease-in-out infinite;
}

.title-container:hover .prompt-symbol,
.title-container:hover .typing-cursor {
    animation: cursorGlow 1s ease-in-out infinite;
}

/* Page Content */
.page-content {
    animation: pageEntry 1s ease-out;
}

@keyframes pageEntry {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* CTA Container */
.cta-container {
    animation: ctaEntry 1s ease-out 0.5s both;
}

@keyframes ctaEntry {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Card Icon */
.card-icon {
    animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

/* Classified Tag */
.classified-tag {
    animation: classifiedGlow 2s ease-in-out infinite;
}

@keyframes classifiedGlow {
    0%, 100% { box-shadow: 0 0 5px var(--primary); }
    50% { box-shadow: 0 0 15px var(--primary); }
}

/* CTA Button */
.cta-button {
    animation: ctaPulse 2s ease-in-out infinite;
}

@keyframes ctaPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Responsive Design for Cypher Title */
@media (max-width: 1024px) {
    .cypher-title {
        font-size: clamp(2.5rem, 6vw, 6rem);
    }

    .prompt-symbol,
    .typing-cursor {
        font-size: 1.5rem;
    }
}

@media (max-width: 768px) {
    .cypher-title {
        font-size: clamp(2rem, 5vw, 4rem);
    }

    .prompt-symbol,
    .typing-cursor {
        font-size: 1.25rem;
    }

    .title-container {
        padding: 2rem 1rem;
    }

    .cta-button {
        padding: 0.75rem 1.5rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {
    .cypher-title {
        font-size: clamp(1.5rem, 4vw, 3rem);
    }

    .prompt-symbol,
    .typing-cursor {
        font-size: 1rem;
    }

    .title-container {
        padding: 1.5rem 0.5rem;
    }
}

/* Additional Utility Classes */
html {
    scroll-behavior: smooth;
}

.bg-black\/\[\.05\] { background-color: rgba(0, 0, 0, 0.05); }
.bg-white\/\[\.06\] { background-color: rgba(255, 255, 255, 0.06); }
.bg-gray-900\/30 { background-color: rgba(17, 24, 39, 0.3); }
.bg-gray-900\/20 { background-color: rgba(17, 24, 39, 0.2); }
.bg-black\/90 { background-color: rgba(0, 0, 0, 0.9); }

/* Dark Mode Invert */
.dark\:invert {
    filter: invert(1);
}

html.dark .dark\:invert,
.dark .dark\:invert {
    filter: invert(0);
}

img.dark\:invert {
    filter: invert(1);
}

/* Matrix Rain Animation */
@keyframes matrix-rain {
    0% { transform: translateY(-100vh); }
    100% { transform: translateY(100vh); }
}

/* Glow Effects */
@keyframes glow-pulse {
    0%, 100% { box-shadow: 0 0 5px var(--primary); }
    50% { box-shadow: 0 0 20px var(--primary); }
}

@keyframes text-glow {
    0%, 100% { text-shadow: 0 0 5px var(--primary); }
    50% { text-shadow: 0 0 20px var(--primary), 0 0 30px var(--primary); }
}

/* Matrix Background */
.matrix-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, var(--background) 0%, var(--card) 100%);
    z-index: -1;
}

.matrix-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 80%, #ffffff 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, #ffffff 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, #ffffff 0%, transparent 50%);
    opacity: 0.1;
    animation: matrix-rain 40s linear infinite;
}

/* Gradient Effects */
.gradient-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Hover Effects */
.hover-glow:hover {
    animation: glow-pulse 1s ease-in-out infinite;
}

.hover-text-glow:hover {
    animation: text-glow 1s ease-in-out infinite;
}

/* Cyber Card */
.cyber-card {
    background: var(--card);
    border: 1px solid var(--border);
    border-radius: 0.5rem;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.cyber-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary), var(--accent), var(--primary));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.cyber-card:hover::before {
    opacity: 1;
}

/* Terminal Cursor */
@keyframes cursor-blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.terminal-cursor::after {
    content: '|';
    animation: cursor-blink 1s infinite;
    color: var(--primary);
    font-weight: bold;
}

/* Loading Animation */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, var(--primary), transparent);
    animation: loading-shimmer 1.5s ease-in-out infinite;
}

@keyframes loading-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Focus Styles */
*:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* Selection */
::selection {
    background: var(--primary);
    color: var(--background);
}

/* Typography Scale */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

/* Links */
a {
    color: var(--primary);
    text-decoration: none;
    transition: all 0.2s ease;
}

a:hover {
    color: var(--accent);
    text-decoration: underline;
}

/* Code Blocks */
code, pre {
    font-family: var(--font-geist-mono);
    background: var(--card);
    border-radius: 0.25rem;
}

pre {
    padding: 1rem;
    overflow-x: auto;
    border: 1px solid var(--border);
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

th, td {
    padding: 0.5rem;
    text-align: left;
    border: 1px solid var(--border);
}

th {
    background: var(--card);
    font-weight: 600;
}

/* Forms */
form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

label {
    font-weight: 500;
    color: var(--foreground);
    margin-bottom: 0.25rem;
}

/* Light Theme Overrides */
.light {
    --background: #ffffff;
    --foreground: #000000;
    --card: #f9fafb;
    --card-foreground: #000000;
    --popover: #ffffff;
    --popover-foreground: #000000;
    --primary: #000000;
    --primary-foreground: #ffffff;
    --primary-hover: #1a1a1a;
    --secondary: #f3f4f6;
    --secondary-foreground: #000000;
    --secondary-hover: #e5e7eb;
    --muted: #f9fafb;
    --muted-foreground: #6b7280;
    --accent: #1e3a8a;
    --accent-foreground: #ffffff;
    --accent-hover: #1e40af;
    --accent-light: #3b82f6;
    --success: #000000;
    --warning: #6b7280;
    --destructive: #dc2626;
    --border: #e5e7eb;
    --border-light: #f3f4f6;
    --border-accent: #1e3a8a;
    --input: #ffffff;
    --ring: #1e3a8a;
    --sidebar: #f9fafb;
    --sidebar-foreground: #000000;
    --sidebar-border: #e5e7eb;
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --gradient-primary: linear-gradient(135deg, #000000, #1a1a1a);
    --gradient-dark: linear-gradient(135deg, #f3f4f6, #ffffff);
    --gradient-card: linear-gradient(135deg, #f9fafb, #f3f4f6);
    --gradient-accent: linear-gradient(135deg, #1e3a8a, #1e40af);
}

.light body {
    background: var(--background);
    color: var(--foreground);
}

.light .bg-black { background-color: #ffffff; }
.light .bg-gray-900 { background-color: #f9fafb; }
.light .bg-gray-800 { background-color: #f3f4f6; }
.light .text-white { color: #111827; }
.light .text-gray-300 { color: #4b5563; }
.light .text-gray-400 { color: #6b7280; }
.light .text-gray-500 { color: #9ca3af; }
.light .border-gray-700 { border-color: #d1d5db; }
.light .border-gray-800 { border-color: #e5e7eb; }

/* Reset and Base */
* {
    box-sizing: border-box;
}

/* Glitch Effect */
.glitch-title {
    position: relative;
    color: #fff;
    font-weight: 700;
    font-size: 45px;
    font-family: 'Fira Code', monospace;
    transform: scale(1.5);
    padding: 20px;
    letter-spacing: 2px;
    text-transform: uppercase;
}



/* Responsive Title */
@media (max-width: 768px) {
    .glitch-title {
        transform: scale(1.2);
        padding: 15px;
        font-size: 35px;
    }
}

@media (max-width: 480px) {
    .glitch-title {
        transform: scale(1);
        padding: 10px;
        font-size: 28px;
    }
}

/* Fade In Animation */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Glow Button */
.glow-btn {
    box-shadow: 0 0 10px var(--primary);
}

@keyframes glowPulse {
    0%, 100% { box-shadow: 0 0 10px var(--primary); }
    50% { box-shadow: 0 0 20px var(--primary), 0 0 30px var(--primary); }
}

/* Matrix Background */
.matrix-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--background);
    z-index: -1;
    overflow: hidden;
}

/* Cyber Container */
.cyber-container {
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

@media (max-width: 600px) {
  .cyber-container { padding: 1.5rem 0.5rem; max-width: 98vw; }
}

/* Neon Line */
.neon-line {
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary), transparent);
    margin: 2rem 0;
    position: relative;
    overflow: hidden;
}

.neon-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, var(--primary), transparent);
    animation: loading-shimmer 2s ease-in-out infinite;
}

/* Cyber Button */
.cyber-btn {
    background: transparent;
    color: var(--primary);
    border: 2px solid var(--primary);
    padding: 1rem 2rem;
    font-family: 'Fira Code', monospace;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

.cyber-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary);
    transition: left 0.3s ease;
    z-index: -1;
}

.cyber-btn:hover, .cyber-btn:focus {
    color: var(--background);
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(255, 255, 255, 0.4);
    border-color: var(--primary);
}

.cyber-btn:hover::before {
    left: 0;
}

.cyber-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(255, 255, 255, 0.2);
}

/* Terminal Shadow */
.terminal-shadow {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

.header-markdown .back-btn {
  background: none;
  border: 2px solid #fff;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 1.5rem;
  transition: border 0.2s, box-shadow 0.2s;
  box-shadow: 0 0 0 transparent;
  margin-right: 1.5rem;
}
.header-markdown .back-btn:hover {
  border: 2px solid #444;
  box-shadow: 0 0 8px #222;
}
.header-markdown .header-title {
  font-size: 2.2rem;
  font-weight: 800;
  color: #fff;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 12px #000, 0 0 8px #222;
  letter-spacing: -0.01em;
}
.header-markdown .breadcrumb {
  font-family: 'Fira Code', monospace;
  color: #b0b0b0;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  opacity: 0.85;
}
.header-markdown .breadcrumb-sep {
  color: #444;
  font-weight: bold;
  margin: 0 0.3rem;
}

@media (max-width: 600px) {
  .header-markdown {
    flex-direction: column;
    gap: 1rem;
    padding: 1.2rem 0 1rem 0;
  }
  .header-markdown .header-title {
    font-size: 1.3rem;
  }
  .header-markdown .breadcrumb {
    font-size: 0.85rem;
  }
  .header-markdown .back-btn {
    width: 2rem;
    height: 2rem;
    font-size: 1.1rem;
    margin-right: 0.7rem;
  }
}

#dashboard-area, #dashboard-area .text-left {
  font-family: 'Fira Code', 'Geist Mono', 'Consolas', monospace;
}

#dashboard-area {
  width: 100%;
  max-width: 100%;
  /* min-height: 100vh; */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  margin: 0 auto !important;
  box-sizing: border-box;
  overflow-x: hidden;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

#dashboard-area > .max-w-2xl {
  margin-left: auto;
  margin-right: auto;
}

body, html {
  overflow-x: hidden;
}

@media (max-width: 900px) {
  #dashboard-area {
    padding: 0.5rem 0 !important;
    max-width: 100vw;
  }
}

/* Espaçamento do dashboard quando a sidebar está visível */
@media (min-width: 900px) {
  #study-sidebar:not([style*='display: none']) ~ main #dashboard-area {
    padding-left: 2.5rem !important;
  }
}

.prose, .prose-invert, .markdown-body, .markdown-content, #markdown-content {
  color: #fff !important;
  background: #000 !important;
}

.prose a, .prose-invert a, .markdown-body a, .markdown-content a, #markdown-content a {
  color: #b0b0b0 !important;
  text-decoration: underline;
}

.prose strong, .prose-invert strong, .markdown-body strong, .markdown-content strong, #markdown-content strong {
  color: #fff !important;
  font-weight: bold;
}

.prose em, .prose-invert em, .markdown-body em, .markdown-content em, #markdown-content em {
  color: #b0b0b0 !important;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6,
.prose-invert h1, .prose-invert h2, .prose-invert h3, .prose-invert h4, .prose-invert h5, .prose-invert h6,
.markdown-body h1, .markdown-body h2, .markdown-body h3, .markdown-body h4, .markdown-body h5, .markdown-body h6,
.markdown-content h1, .markdown-content h2, .markdown-content h3, .markdown-content h4, .markdown-content h5, .markdown-content h6,
#markdown-content h1, #markdown-content h2, #markdown-content h3, #markdown-content h4, #markdown-content h5, #markdown-content h6 {
  color: #fff !important;
}

.prose code, .prose-invert code, .markdown-body code, .markdown-content code, #markdown-content code {
  color: #b0b0b0 !important;
  background: #181818 !important;
  border-radius: 4px;
}

.prose pre, .prose-invert pre, .markdown-body pre, .markdown-content pre, #markdown-content pre {
  color: #fff !important;
  background: #181818 !important;
  border-radius: 6px;
}

.prose blockquote, .prose-invert blockquote, .markdown-body blockquote, .markdown-content blockquote, #markdown-content blockquote {
  color: #b0b0b0 !important;
  border-left: 4px solid #444 !important;
  background: #111 !important;
}

.prose hr, .prose-invert hr, .markdown-body hr, .markdown-content hr, #markdown-content hr {
  border-color: #333 !important;
  background: #333 !important;
}

.prose ul li::before, .prose ol li::before, .prose-invert ul li::before, .prose-invert ol li::before, .markdown-body ul li::before, .markdown-body ol li::before, .markdown-content ul li::before, .markdown-content ol li::before, #markdown-content ul li::before, #markdown-content ol li::before {
  color: #b0b0b0 !important;
}

/* Remover/acobertar qualquer cor fora da paleta */
.prose [style*="color:"], .prose-invert [style*="color:"], .markdown-body [style*="color:"], .markdown-content [style*="color:"], #markdown-content [style*="color:"] {
  color: #fff !important;
}

/* Remover azul de headings, caixas e linhas em markdown */
.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6,
.prose-invert h1, .prose-invert h2, .prose-invert h3, .prose-invert h4, .prose-invert h5, .prose-invert h6,
.markdown-body h1, .markdown-body h2, .markdown-body h3, .markdown-body h4, .markdown-body h5, .markdown-body h6,
.markdown-content h1, .markdown-content h2, .markdown-content h3, .markdown-content h4, .markdown-content h5, .markdown-content h6,
#markdown-content h1, #markdown-content h2, #markdown-content h3, #markdown-content h4, #markdown-content h5, #markdown-content h6 {
  border-bottom: 2px solid #333 !important;
  background: none !important;
}

.prose hr, .prose-invert hr, .markdown-body hr, .markdown-content hr, #markdown-content hr {
  border-color: #333 !important;
  background: #333 !important;
}

.prose blockquote, .prose-invert blockquote, .markdown-body blockquote, .markdown-content blockquote, #markdown-content blockquote {
  border-left: 4px solid #444 !important;
  background: #111 !important;
}

.prose [style*="background: linear-gradient"],
.prose-invert [style*="background: linear-gradient"],
.markdown-body [style*="background: linear-gradient"],
.markdown-content [style*="background: linear-gradient"],
#markdown-content [style*="background: linear-gradient"] {
  background: none !important;
}

/* Forçar paleta neutra rigorosa em markdown */
.prose, .prose-invert, .markdown-body, .markdown-content, #markdown-content,
.prose *, .prose-invert *, .markdown-body *, .markdown-content *, #markdown-content * {
  background: none !important;
  background-image: none !important;
  box-shadow: none !important;
  border-color: #333 !important;
  color: #fff !important;
}

.prose a, .prose-invert a, .markdown-body a, .markdown-content a, #markdown-content a {
  color: #b0b0b0 !important;
}

.prose strong, .prose-invert strong, .markdown-body strong, .markdown-content strong, #markdown-content strong {
  color: #fff !important;
}

.prose em, .prose-invert em, .markdown-body em, .markdown-content em, #markdown-content em {
  color: #b0b0b0 !important;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6,
.prose-invert h1, .prose-invert h2, .prose-invert h3, .prose-invert h4, .prose-invert h5, .prose-invert h6,
.markdown-body h1, .markdown-body h2, .markdown-body h3, .markdown-body h4, .markdown-body h5, .markdown-body h6,
.markdown-content h1, .markdown-content h2, .markdown-content h3, .markdown-content h4, .markdown-content h5, .markdown-content h6,
#markdown-content h1, #markdown-content h2, #markdown-content h3, #markdown-content h4, #markdown-content h5, #markdown-content h6 {
  color: #fff !important;
  border-bottom: 2px solid #333 !important;
  background: none !important;
  background-image: none !important;
  box-shadow: none !important;
}

.prose hr, .prose-invert hr, .markdown-body hr, .markdown-content hr, #markdown-content hr {
  border-color: #333 !important;
  background: #333 !important;
}

.prose blockquote, .prose-invert blockquote, .markdown-body blockquote, .markdown-content blockquote, #markdown-content blockquote {
  color: #b0b0b0 !important;
  border-left: 4px solid #444 !important;
  background: #111 !important;
  background-image: none !important;
}

.prose ul li::before, .prose ol li::before, .prose-invert ul li::before, .prose-invert ol li::before, .markdown-body ul li::before, .markdown-body ol li::before, .markdown-content ul li::before, .markdown-content ol li::before, #markdown-content ul li::before, #markdown-content ol li::before {
  color: #b0b0b0 !important;
}

.prose ol, .prose ul, .prose-invert ol, .prose-invert ul, .markdown-body ol, .markdown-body ul, .markdown-content ol, .markdown-content ul, #markdown-content ol, #markdown-content ul {
  list-style-position: outside !important;
  padding-left: 2em !important;
  margin-left: 0 !important;
}

.prose ol li::before, .prose ul li::before, .prose-invert ol li::before, .prose-invert ul li::before, .markdown-body ol li::before, .markdown-body ul li::before, .markdown-content ol li::before, .markdown-content ul li::before, #markdown-content ol li::before, #markdown-content ul li::before {
  display: none !important;
  content: none !important;
}

.prose ol li, .prose-invert ol li, .markdown-body ol li, .markdown-content ol li, #markdown-content ol li {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

/* Links em markdown: nunca azul */
.prose a, .prose-invert a, .markdown-body a, .markdown-content a, #markdown-content a,
.prose a:visited, .prose-invert a:visited, .markdown-body a:visited, .markdown-content a:visited, #markdown-content a:visited,
.prose a:active, .prose-invert a:active, .markdown-body a:active, .markdown-content a:active, #markdown-content a:active,
.prose a:hover, .prose-invert a:hover, .markdown-body a:hover, .markdown-content a:hover, #markdown-content a:hover {
  color: #b0b0b0 !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Seleção de texto: cinza */
.prose ::selection, .prose-invert ::selection, .markdown-body ::selection, .markdown-content ::selection, #markdown-content ::selection {
  background: #333 !important;
  color: #fff !important;
}

/* Remover outline/box-shadow azul de qualquer elemento focado */
.prose *:focus, .prose-invert *:focus, .markdown-body *:focus, .markdown-content *:focus, #markdown-content *:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* Forçar SVGs a ficarem brancos/cinza em markdown */
.prose svg, .prose-invert svg, .markdown-body svg, .markdown-content svg, #markdown-content svg {
  color: #b0b0b0 !important;
  fill: #b0b0b0 !important;
  stroke: #b0b0b0 !important;
}

/* Dashboard Styles */
#dashboard-area {
    background: linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #000000 100%);
    min-height: 100vh;
    position: relative;
}

#dashboard-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(0, 255, 231, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 255, 231, 0.02) 0%, transparent 50%);
    pointer-events: none;
}

/* Hero Section */
#dashboard-area .text-5xl {
    background: linear-gradient(135deg, #ffffff 0%, #e5e5e5 50%, #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
}

/* Stats Cards */
#dashboard-area .bg-gradient-to-br {
    position: relative;
    overflow: hidden;
}

#dashboard-area .bg-gradient-to-br::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    transition: left 0.5s ease;
}

#dashboard-area .bg-gradient-to-br:hover::before {
    left: 100%;
}

#dashboard-area .bg-gradient-to-br:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Content Cards */
#dashboard-area .border-gray-800 {
    border-color: #1f1f1f;
    transition: all 0.3s ease;
}

#dashboard-area .border-gray-800:hover {
    border-color: #333;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

/* Icons */
#dashboard-area i[data-lucide] {
    filter: drop-shadow(0 0 8px currentColor);
}

/* Animated Elements */
#dashboard-area .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    #dashboard-area .text-5xl {
        font-size: 2.5rem;
    }
    
    #dashboard-area .text-6xl {
        font-size: 3rem;
    }
    
    #dashboard-area .grid-cols-4 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 640px) {
    #dashboard-area .grid-cols-4 {
        grid-template-columns: 1fr;
    }
    
    #dashboard-area .lg\:grid-cols-2 {
        grid-template-columns: 1fr;
    }
}

/* Enhanced Typography */
#dashboard-area .text-gray-300 {
    color: #d1d5db;
    line-height: 1.7;
}

#dashboard-area .text-gray-400 {
    color: #9ca3af;
}

#dashboard-area .text-gray-500 {
    color: #6b7280;
}

/* Gradient Text Support */
.bg-gradient-to-r {
    background: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-white {
    --tw-gradient-from: #ffffff;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(255, 255, 255, 0));
}

.to-gray-300 {
    --tw-gradient-to: #d1d5db;
}

.bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
}

.text-transparent {
    color: transparent;
}

/* Enhanced Hover Effects */
#dashboard-area .hover\:border-gray-700:hover {
    border-color: #374151;
}

/* Status Indicators */
#dashboard-area .bg-green-500 {
    background-color: #10b981;
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

/* Quote Box */
#dashboard-area .border-l-4 {
    border-left-width: 4px;
}

#dashboard-area .border-blue-400 {
    border-color: #60a5fa;
}

/* Footer Enhancements */
#dashboard-area .border-t {
    border-top-width: 1px;
}

#dashboard-area .border-gray-800 {
    border-color: #1f2937;
}

/* Loading States */
#dashboard-area .loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Focus States */
#dashboard-area *:focus {
    outline: 2px solid #60a5fa;
    outline-offset: 2px;
}

/* Selection */
#dashboard-area ::selection {
    background-color: rgba(96, 165, 250, 0.3);
    color: #ffffff;
}

/* Scrollbar for Dashboard */
#dashboard-area::-webkit-scrollbar {
    width: 8px;
}

#dashboard-area::-webkit-scrollbar-track {
    background: #0a0a0a;
}

#dashboard-area::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 4px;
}

#dashboard-area::-webkit-scrollbar-thumb:hover {
    background: #555;
}
