// CYPHER - Cyberpunk Effects JavaScript

class CyberpunkEffects {
    constructor() {
        this.init();
    }

    init() {
        this.setupMatrixRain();
        this.setupGlitchEffects();
        this.setupTypingEffect();
        this.setupParticleSystem();
    }

    // Matrix Rain Effect
    setupMatrixRain() {
        const matrixContainer = document.querySelector('.matrix-rain');
        if (!matrixContainer) return;

        const characters = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
        const columns = Math.floor(window.innerWidth / 20);
        
        for (let i = 0; i < columns; i++) {
            this.createMatrixColumn(matrixContainer, characters, i);
        }
    }

    createMatrixColumn(container, characters, index) {
        const column = document.createElement('div');
        column.style.position = 'absolute';
        column.style.left = `${index * 20}px`;
        column.style.top = '-100px';
        column.style.color = '#ffffff';
        column.style.fontFamily = 'var(--font-geist-mono)';
        column.style.fontSize = '14px';
        column.style.opacity = '0.1';
        column.style.animation = `matrix-fall ${15 + Math.random() * 20}s linear infinite`;
        column.style.animationDelay = `${Math.random() * 10}s`;

        // Add random characters
        for (let i = 0; i < 20; i++) {
            const char = document.createElement('div');
            char.textContent = characters[Math.floor(Math.random() * characters.length)];
            char.style.lineHeight = '1.2';
            column.appendChild(char);
        }

        container.appendChild(column);

        // Update characters periodically
        setInterval(() => {
            const chars = column.querySelectorAll('div');
            chars.forEach(char => {
                if (Math.random() < 0.05) {
                    char.textContent = characters[Math.floor(Math.random() * characters.length)];
                }
            });
        }, 200);
    }

    // Glitch Effects
    setupGlitchEffects() {
        const glitchElements = document.querySelectorAll('.glitch');
        
        glitchElements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                this.triggerGlitch(element);
            });
        });
    }

    triggerGlitch(element) {
        element.style.animation = 'none';
        setTimeout(() => {
            element.style.animation = 'glitch-skew 0.3s ease-in-out';
        }, 10);
    }

    // Typing Effect
    setupTypingEffect() {
        const typingElements = document.querySelectorAll('.terminal-cursor');
        
        typingElements.forEach(element => {
            this.createTypingEffect(element);
        });
    }

    createTypingEffect(element) {
        const text = element.textContent;
        element.textContent = '';
        
        let i = 0;
        const typeInterval = setInterval(() => {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
            } else {
                clearInterval(typeInterval);
                element.classList.add('terminal-cursor');
            }
        }, 100);
    }

    // Particle System
    setupParticleSystem() {
        this.createParticleCanvas();
    }

    createParticleCanvas() {
        const canvas = document.createElement('canvas');
        canvas.style.position = 'fixed';
        canvas.style.top = '0';
        canvas.style.left = '0';
        canvas.style.width = '100%';
        canvas.style.height = '100%';
        canvas.style.pointerEvents = 'none';
        canvas.style.zIndex = '1';
        canvas.style.opacity = '0.05';
        
        document.body.appendChild(canvas);
        
        const ctx = canvas.getContext('2d');
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        
        const particles = [];
        const particleCount = 50;
        
        // Create particles
        for (let i = 0; i < particleCount; i++) {
            particles.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                vx: (Math.random() - 0.5) * 0.5,
                vy: (Math.random() - 0.5) * 0.5,
                size: Math.random() * 2 + 1
            });
        }
        
        // Animation loop
        const animate = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#ffffff';
            
            particles.forEach(particle => {
                // Update position
                particle.x += particle.vx;
                particle.y += particle.vy;
                
                // Wrap around edges
                if (particle.x < 0) particle.x = canvas.width;
                if (particle.x > canvas.width) particle.x = 0;
                if (particle.y < 0) particle.y = canvas.height;
                if (particle.y > canvas.height) particle.y = 0;
                
                // Draw particle
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fill();
            });
            
            // Draw connections
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 0.5;
            
            for (let i = 0; i < particles.length; i++) {
                for (let j = i + 1; j < particles.length; j++) {
                    const dx = particles[i].x - particles[j].x;
                    const dy = particles[i].y - particles[j].y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < 100) {
                        ctx.beginPath();
                        ctx.moveTo(particles[i].x, particles[i].y);
                        ctx.lineTo(particles[j].x, particles[j].y);
                        ctx.stroke();
                    }
                }
            }
            
            requestAnimationFrame(animate);
        };
        
        animate();
        
        // Resize handler
        window.addEventListener('resize', () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        });
    }

    // Utility Methods
    static addHoverGlow(element) {
        element.addEventListener('mouseenter', () => {
            element.style.boxShadow = '0 0 20px var(--primary-glow)';
        });
        
        element.addEventListener('mouseleave', () => {
            element.style.boxShadow = '';
        });
    }

    static addClickRipple(element) {
        element.addEventListener('click', (e) => {
            const ripple = document.createElement('div');
            const rect = element.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.position = 'absolute';
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.style.background = 'var(--primary-glow)';
            ripple.style.borderRadius = '50%';
            ripple.style.transform = 'scale(0)';
            ripple.style.animation = 'ripple 0.6s linear';
            ripple.style.pointerEvents = 'none';
            
            element.style.position = 'relative';
            element.style.overflow = 'hidden';
            element.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    }
}

// CSS for ripple animation
const rippleCSS = `
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
`;

// Add ripple CSS to document
const style = document.createElement('style');
style.textContent = rippleCSS;
document.head.appendChild(style);

// Initialize effects when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CyberpunkEffects();
    
    // Add effects to buttons
    const buttons = document.querySelectorAll('button, .btn-primary, .btn-secondary');
    buttons.forEach(button => {
        CyberpunkEffects.addHoverGlow(button);
        CyberpunkEffects.addClickRipple(button);
    });
});

// Export for use in other scripts
window.CyberpunkEffects = CyberpunkEffects;
