{% extends "base.html" %}

{% block title %}CYPHER - Learning Protocol{% endblock %}
{% block description %}Sistema de aprendizado de cibersegurança CYPHER{% endblock %}

{% block content %}
<div class="min-h-screen relative" style="background: var(--background); color: var(--foreground);">
    <!-- Header -->
    <header class="p-4 backdrop-blur-sm relative z-10" style="border-bottom: 1px solid var(--border); background: var(--background);">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
                <!-- Back Button -->
                <a href="/" class="flex items-center gap-2 px-3 py-2 rounded-lg transition-all hover:bg-secondary" style="border: 1px solid var(--border);">
                    <i data-lucide="arrow-left" class="w-4 h-4"></i>
                    <span class="font-mono text-sm">HOME</span>
                </a>

                <div class="flex items-center gap-2">
                    <i data-lucide="terminal" class="w-6 h-6 text-primary"></i>
                    <span class="text-xl font-bold">CYPHER</span>
                    <span class="text-muted font-mono">/ learning_protocol</span>
                </div>
            </div>
            <div class="flex items-center gap-4">
                <div class="flex items-center gap-2 text-muted font-mono text-sm">
                    <i id="connection-icon" data-lucide="wifi" class="w-4 h-4"></i>
                    <span id="connection-status">CONNECTED</span>
                </div>
                <div id="current-time" class="text-muted font-mono text-sm"></div>
                <div class="flex items-center gap-2 font-mono">
                    <i data-lucide="user" class="w-4 h-4 text-white"></i>
                    <span>@student</span>
                </div>
            </div>
        </div>
    </header>

    <div id="show-sidebar-btn" class="fixed top-6 left-4 z-50 hidden">
        <button class="text-white font-mono leading-none bg-transparent border-none shadow-none p-0 m-0 hover:bg-transparent focus:outline-none" style="background:none!important;border:none!important;box-shadow:none!important;font-size:3rem;line-height:1;" title="Mostrar barra lateral">→</button>
    </div>

    <div class="flex" style="height: calc(100vh - 80px);">
        <!-- Professional Sidebar -->
        <aside id="study-sidebar" class="w-80 h-full overflow-y-auto relative transition-all duration-300" style="background: var(--sidebar); border-right: 1px solid var(--border);">
            <!-- Sidebar Header -->
            <div class="p-4 relative z-10" style="border-bottom: 1px solid var(--border);">
                <div class="flex items-center justify-between gap-2 mb-3">
                    <div class="flex items-center gap-2">
                    <i data-lucide="layers" class="w-5 h-5 text-white"></i>
                    <h2 class="text-lg font-bold text-primary">CYPHER_MODULES</h2>
                    </div>
                    <!-- Botão para esconder a sidebar -->
                    <button id="toggle-sidebar" class="text-white font-mono leading-none bg-transparent border-none shadow-none p-0 m-0 hover:bg-transparent focus:outline-none flex-shrink-0" style="background:none!important;border:none!important;box-shadow:none!important;font-size:2rem;line-height:1;" title="Esconder barra lateral">←</button>
                </div>
                <div class="flex items-center gap-2 text-xs text-muted font-mono">
                    <i data-lucide="terminal" class="w-3 h-3"></i>
                    <span>root@cypher:/learning#</span>
                </div>
                <div class="mt-2 flex items-center gap-2">
                    <div class="w-2 h-2 rounded-full" style="background: var(--primary);"></div>
                    <span class="text-xs text-muted font-mono">SYSTEM_ACTIVE</span>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="p-4 relative z-10" id="sidebar-nav">
                <!-- Sidebar content will be populated by JavaScript -->
            </nav>

            <!-- Professional Footer -->
            <div class="p-4 mt-auto relative z-10" style="border-top: 1px solid var(--border);">
                <div class="text-xs font-mono text-muted space-y-2">
                    <div class="flex items-center gap-2">
                        <i data-lucide="user" class="w-3 h-3 text-muted"></i>
                        <span>cyber_student@cypher</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <i data-lucide="clock" class="w-3 h-3 text-muted"></i>
                        <span id="session-time">Session: 00:00:00</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <i data-lucide="activity" class="w-3 h-3 text-white"></i>
                        <span class="text-secondary-foreground">STATUS: READY</span>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Professional Main Content -->
        <main class="flex-1 h-full overflow-y-auto relative">
            <!-- Content Area -->
            <div id="content-area" class="hidden p-6 h-full overflow-y-auto">
                <div class="w-full">
                    <!-- Content Header -->
                    <div class="mb-6 pb-4 header-markdown" style="border-bottom: 1.5px solid #222; background: rgba(0,0,0,0.85); display: flex; align-items: center; gap: 2rem; padding: 2rem 0 1.5rem 0; position: sticky; top: 0; z-index: 10;">
                        <button id="back-to-dashboard" class="back-btn" style="background: none; border: 2px solid #fff; border-radius: 50%; width: 2.5rem; height: 2.5rem; display: flex; align-items: center; justify-content: center; color: #fff; font-size: 1.5rem; transition: border 0.2s, box-shadow 0.2s; box-shadow: 0 0 0 transparent; margin-right: 1.5rem;">
                            <i data-lucide="arrow-left"></i>
                        </button>
                        <div style="flex:1;">
                            <h1 id="content-title" class="header-title" style="font-size: 2.7rem; font-weight: 800; color: #fff; margin-bottom: 0.5rem; text-shadow: 0 2px 12px #000, 0 0 8px #00ffe7; letter-spacing: -0.01em;"></h1>
                            <div id="content-path" class="breadcrumb" style="font-family: 'Fira Code', monospace; color: #b0b0b0; font-size: 1rem; display: flex; align-items: center; gap: 0.5rem; opacity: 0.85;"></div>
                        </div>
                    </div>

                    <!-- Markdown Content -->
                    <div id="markdown-content" class="prose prose-invert w-full px-6 py-12">
                        <!-- Conteúdo markdown será carregado aqui -->
                    </div>
                </div>
            </div>

            <!-- Dashboard Area -->
            <div id="dashboard-area" class="min-h-screen p-6 bg-black">
                <!-- Dashboard Header -->
                <div class="mb-6 pb-4 header-markdown" style="border-bottom: 1.5px solid #222; background: rgba(0,0,0,0.85); display: flex; align-items: center; gap: 2rem; padding: 2rem 0 1.5rem 0; position: sticky; top: 0; z-index: 10;">
                    <div style="flex:1;">
                        <h1 class="header-title" style="font-size: 2.7rem; font-weight: 800; color: #fff; margin-bottom: 0.5rem; text-shadow: 0 2px 12px #000, 0 0 8px #00ffe7; letter-spacing: -0.01em;">Dashboard do Cypher</h1>
                        <div class="breadcrumb" style="font-family: 'Fira Code', monospace; color: #b0b0b0; font-size: 1rem; display: flex; align-items: center; gap: 0.5rem; opacity: 0.85;">
                            <i data-lucide="home" class="w-4 h-4"></i>
                            <span>Painel Principal</span>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Markdown Content -->
                <div id="dashboard-markdown-content" class="prose prose-invert w-full px-6 py-12">
                    <!-- Conteúdo markdown do dashboard será carregado aqui -->
                </div>
            </div>
        </main>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/estudos.js') }}"></script>
{% endblock %}
