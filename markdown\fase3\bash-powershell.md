# 3.2 - Bash e PowerShell

## Fundamentos de Scripting

### Conceitos Básicos

#### O que é Scripting
- **Definição**: Automação de tarefas através de scripts
- **Vantagens**:
  - Repetibilidade: Executar tarefas repetitivas
  - Consistência: Mesmo resultado sempre
  - Eficiência: Economia de tempo
  - Documentação: Scripts como documentação

#### Tipos de Scripts
1. **Batch Scripts**: Execução em lote
2. **Automation Scripts**: Automação de processos
3. **Maintenance Scripts**: Manutenção de sistemas
4. **Security Scripts**: Tarefas de segurança

## Bash Scripting (Linux/Unix)

### Características do Bash
- **Shell Padrão**: Shell padrão em sistemas Unix/Linux
- **Interpretado**: Execução linha por linha
- **Poderoso**: Grande flexibilidade e recursos
- **Portável**: Funciona em diferentes sistemas Unix

### Sintaxe Básica

#### Shebang e Estrutura
```bash
#!/bin/bash
# Comentário: Este é um script bash

# Variáveis
nome="João"
idade=25

# Saída
echo "Olá, $nome!"
echo "Você tem $idade anos"
```

#### Variáveis
```bash
# Declaração
nome="João"
idade=25

# Uso
echo "Nome: $nome"
echo "Idade: ${idade}"

# Variáveis de ambiente
echo "Usuário: $USER"
echo "Diretório: $PWD"
echo "PATH: $PATH"

# Comando substituição
data=$(date)
arquivos=$(ls -la)
```

#### Estruturas de Controle

##### Condicionais
```bash
# If simples
if [ $idade -ge 18 ]; then
    echo "Maior de idade"
fi

# If-else
if [ -f "arquivo.txt" ]; then
    echo "Arquivo existe"
else
    echo "Arquivo não existe"
fi

# If-elif-else
if [ $nota -ge 9 ]; then
    echo "Excelente"
elif [ $nota -ge 7 ]; then
    echo "Bom"
elif [ $nota -ge 5 ]; then
    echo "Regular"
else
    echo "Insuficiente"
fi
```

##### Loops
```bash
# For loop
for i in {1..5}; do
    echo "Número: $i"
done

# For com lista
for fruta in maçã banana laranja; do
    echo "Fruta: $fruta"
done

# While loop
contador=1
while [ $contador -le 5 ]; do
    echo "Contador: $contador"
    contador=$((contador + 1))
done
```

### Operadores de Teste

#### Operadores de Arquivo
```bash
# Testes de arquivo
[ -f arquivo.txt ]    # Arquivo existe
[ -d diretorio ]      # Diretório existe
[ -r arquivo.txt ]    # Arquivo é legível
[ -w arquivo.txt ]    # Arquivo é gravável
[ -x arquivo.txt ]    # Arquivo é executável
[ -s arquivo.txt ]    # Arquivo não está vazio
```

#### Operadores de String
```bash
# Testes de string
[ -z "$variavel" ]    # String está vazia
[ -n "$variavel" ]    # String não está vazia
[ "$str1" = "$str2" ] # Strings são iguais
[ "$str1" != "$str2" ] # Strings são diferentes
```

#### Operadores Numéricos
```bash
# Testes numéricos
[ $a -eq $b ]    # Igual
[ $a -ne $b ]    # Diferente
[ $a -lt $b ]    # Menor que
[ $a -le $b ]    # Menor ou igual
[ $a -gt $b ]    # Maior que
[ $a -ge $b ]    # Maior ou igual
```

### Funções
```bash
# Definição de função
saudacao() {
    local nome=$1
    echo "Olá, $nome!"
}

# Chamada
saudacao "João"

# Função com retorno
soma() {
    local a=$1
    local b=$2
    echo $((a + b))
}

resultado=$(soma 5 3)
echo "Resultado: $resultado"
```

## PowerShell (Windows)

### Características do PowerShell
- **Shell Moderno**: Sucessor do CMD
- **Orientado a Objetos**: Trabalha com objetos .NET
- **Consistente**: Verbos padronizados
- **Extensível**: Módulos e snap-ins

### Sintaxe Básica

#### Variáveis
```powershell
# Declaração
$nome = "João"
$idade = 25

# Uso
Write-Host "Nome: $nome"
Write-Host "Idade: $($idade)"

# Variáveis de ambiente
Write-Host "Usuário: $env:USERNAME"
Write-Host "Computador: $env:COMPUTERNAME"

# Comando substituição
$data = Get-Date
$arquivos = Get-ChildItem
```

#### Estruturas de Controle

##### Condicionais
```powershell
# If simples
if ($idade -ge 18) {
    Write-Host "Maior de idade"
}

# If-else
if (Test-Path "arquivo.txt") {
    Write-Host "Arquivo existe"
} else {
    Write-Host "Arquivo não existe"
}

# If-elseif-else
if ($nota -ge 9) {
    Write-Host "Excelente"
} elseif ($nota -ge 7) {
    Write-Host "Bom"
} elseif ($nota -ge 5) {
    Write-Host "Regular"
} else {
    Write-Host "Insuficiente"
}
```

##### Loops
```powershell
# For loop
for ($i = 1; $i -le 5; $i++) {
    Write-Host "Número: $i"
}

# ForEach
$frutas = @("maçã", "banana", "laranja")
foreach ($fruta in $frutas) {
    Write-Host "Fruta: $fruta"
}

# While loop
$contador = 1
while ($contador -le 5) {
    Write-Host "Contador: $contador"
    $contador++
}
```

### Operadores de Comparação
```powershell
# Operadores de comparação
$a -eq $b    # Igual
$a -ne $b    # Diferente
$a -lt $b    # Menor que
$a -le $b    # Menor ou igual
$a -gt $b    # Maior que
$a -ge $b    # Maior ou igual

# Operadores de string
$str1 -eq $str2    # Igual
$str1 -like "*test*"    # Like (wildcard)
$str1 -match "regex"    # Match (regex)
```

### Funções
```powershell
# Definição de função
function Saudacao {
    param([string]$nome)
    Write-Host "Olá, $nome!"
}

# Chamada
Saudacao -nome "João"

# Função com retorno
function Soma {
    param([int]$a, [int]$b)
    return $a + $b
}

$resultado = Soma -a 5 -b 3
Write-Host "Resultado: $resultado"
```

## Aplicações em Segurança

### Scripts de Monitoramento

#### Monitoramento de Logs (Bash)
```bash
#!/bin/bash
# monitor_logs.sh

LOG_FILE="/var/log/auth.log"
ALERT_FILE="/tmp/auth_alerts.txt"

# Monitorar tentativas de login falhadas
tail -f $LOG_FILE | while read line; do
    if echo "$line" | grep -q "Failed password"; then
        echo "$(date): $line" >> $ALERT_FILE
        echo "ALERTA: Tentativa de login falhada detectada!"
    fi
done
```

#### Monitoramento de Processos (PowerShell)
```powershell
# monitor_processes.ps1

function Monitor-Processes {
    param([string]$ProcessName)
    
    while ($true) {
        $processes = Get-Process -Name $ProcessName -ErrorAction SilentlyContinue
        
        if ($processes) {
            Write-Host "$(Get-Date): Processo $ProcessName encontrado"
            $processes | ForEach-Object {
                Write-Host "  PID: $($_.Id), CPU: $($_.CPU), Memory: $($_.WorkingSet)"
            }
        }
        
        Start-Sleep -Seconds 30
    }
}

# Uso
Monitor-Processes -ProcessName "notepad"
```

### Scripts de Hardening

#### Hardening de Sistema (Bash)
```bash
#!/bin/bash
# hardening_system.sh

echo "Iniciando hardening do sistema..."

# Desabilitar serviços desnecessários
systemctl disable telnet
systemctl disable rsh
systemctl disable rlogin

# Configurar firewall básico
iptables -F
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT
iptables -A INPUT -i lo -j ACCEPT
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT
iptables -A INPUT -p tcp --dport 22 -j ACCEPT

# Configurar permissões de arquivos
chmod 600 /etc/shadow
chmod 644 /etc/passwd
chmod 644 /etc/group

echo "Hardening concluído!"
```

#### Hardening de Windows (PowerShell)
```powershell
# hardening_windows.ps1

Write-Host "Iniciando hardening do Windows..."

# Desabilitar serviços desnecessários
$services = @("Telnet", "TlntSvr", "RemoteRegistry")
foreach ($service in $services) {
    Set-Service -Name $service -StartupType Disabled
    Stop-Service -Name $service -Force
}

# Configurar políticas de senha
secedit /export /cfg C:\secpol.cfg
$secpol = Get-Content C:\secpol.cfg
$secpol = $secpol -replace "MinimumPasswordAge = 0", "MinimumPasswordAge = 1"
$secpol = $secpol -replace "MinimumPasswordLength = 0", "MinimumPasswordLength = 8"
$secpol | Out-File C:\secpol.cfg
secedit /configure /db C:\Windows\security\local.sdb /cfg C:\secpol.cfg /areas SECURITYPOLICY

# Configurar auditoria
auditpol /set /category:* /success:enable /failure:enable

Write-Host "Hardening concluído!"
```

### Scripts de Análise

#### Análise de Logs (Bash)
```bash
#!/bin/bash
# analyze_logs.sh

LOG_FILE="/var/log/auth.log"
REPORT_FILE="/tmp/auth_report.txt"

echo "=== Relatório de Análise de Logs ===" > $REPORT_FILE
echo "Data: $(date)" >> $REPORT_FILE
echo "" >> $REPORT_FILE

# Tentativas de login falhadas por IP
echo "=== Tentativas de Login Falhadas por IP ===" >> $REPORT_FILE
grep "Failed password" $LOG_FILE | \
    awk '{print $11}' | \
    sort | uniq -c | \
    sort -nr | head -10 >> $REPORT_FILE

echo "" >> $REPORT_FILE

# Logins bem-sucedidos
echo "=== Logins Bem-sucedidos ===" >> $REPORT_FILE
grep "Accepted password" $LOG_FILE | \
    awk '{print $1, $2, $3, $11}' | \
    tail -20 >> $REPORT_FILE

echo "Relatório gerado em: $REPORT_FILE"
```

#### Análise de Eventos (PowerShell)
```powershell
# analyze_events.ps1

function Analyze-SecurityEvents {
    param([int]$Hours = 24)
    
    $startTime = (Get-Date).AddHours(-$Hours)
    
    # Eventos de segurança
    $events = Get-WinEvent -FilterHashtable @{
        LogName = 'Security'
        StartTime = $startTime
    }
    
    Write-Host "=== Análise de Eventos de Segurança ==="
    Write-Host "Período: Últimas $Hours horas"
    Write-Host ""
    
    # Eventos por tipo
    $events | Group-Object -Property Id | Sort-Object Count -Descending | ForEach-Object {
        Write-Host "Event ID $($_.Name): $($_.Count) ocorrências"
    }
    
    # Logins falhados
    $failedLogins = $events | Where-Object { $_.Id -eq 4625 }
    Write-Host ""
    Write-Host "Logins falhados: $($failedLogins.Count)"
    
    # Logins bem-sucedidos
    $successfulLogins = $events | Where-Object { $_.Id -eq 4624 }
    Write-Host "Logins bem-sucedidos: $($successfulLogins.Count)"
}

# Uso
Analyze-SecurityEvents -Hours 24
```

### Scripts de Backup

#### Backup Automático (Bash)
```bash
#!/bin/bash
# backup_security.sh

BACKUP_DIR="/backup/security"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="security_backup_$DATE.tar.gz"

# Criar diretório de backup
mkdir -p $BACKUP_DIR

# Arquivos importantes para backup
tar -czf "$BACKUP_DIR/$BACKUP_NAME" \
    /etc/passwd \
    /etc/shadow \
    /etc/group \
    /etc/ssh/sshd_config \
    /var/log/auth.log \
    /etc/iptables/rules.v4

# Manter apenas os últimos 7 backups
find $BACKUP_DIR -name "security_backup_*.tar.gz" -mtime +7 -delete

echo "Backup criado: $BACKUP_NAME"
```

#### Backup de Configurações (PowerShell)
```powershell
# backup_config.ps1

function Backup-SecurityConfig {
    param([string]$BackupPath = "C:\Backup\Security")
    
    $date = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupName = "security_config_$date.zip"
    $backupFile = Join-Path $BackupPath $backupName
    
    # Criar diretório de backup
    if (!(Test-Path $BackupPath)) {
        New-Item -ItemType Directory -Path $BackupPath -Force
    }
    
    # Arquivos para backup
    $files = @(
        "$env:SYSTEMROOT\System32\config\SAM"
        "$env:SYSTEMROOT\System32\config\SYSTEM"
        "$env:SYSTEMROOT\System32\config\SECURITY"
        "$env:SYSTEMROOT\System32\drivers\etc\hosts"
    )
    
    # Criar backup
    Compress-Archive -Path $files -DestinationPath $backupFile -Force
    
    # Limpar backups antigos (mais de 7 dias)
    Get-ChildItem -Path $BackupPath -Name "security_config_*.zip" | ForEach-Object {
        $file = Join-Path $BackupPath $_
        if ((Get-Date) - (Get-Item $file).LastWriteTime).Days -gt 7) {
            Remove-Item $file -Force
        }
    }
    
    Write-Host "Backup criado: $backupFile"
}

# Uso
Backup-SecurityConfig
```

## Boas Práticas

### Segurança
- **Validação de Entrada**: Sempre validar parâmetros
- **Princípio do Menor Privilégio**: Executar com privilégios mínimos
- **Logs de Auditoria**: Registrar ações importantes
- **Sanitização**: Limpar dados de entrada

### Manutenibilidade
- **Comentários**: Documentar código complexo
- **Modularização**: Dividir scripts em funções
- **Tratamento de Erros**: Sempre tratar exceções
- **Nomes Descritivos**: Usar nomes claros para variáveis

### Performance
- **Eficiência**: Evitar loops desnecessários
- **Recursos**: Monitorar uso de recursos
- **Timeout**: Definir timeouts para operações longas
- **Paralelização**: Usar quando apropriado

## Conclusão

Bash e PowerShell fornecem:

- **Automação Poderosa**: Automatizar tarefas de segurança
- **Flexibilidade**: Adaptação a diferentes cenários
- **Integração**: Trabalhar com sistemas existentes
- **Eficiência**: Economia de tempo e recursos

Estes conhecimentos são essenciais para:
- Automação de processos de segurança
- Monitoramento de sistemas
- Análise de logs e eventos
- Hardening de sistemas
- Resposta a incidentes

O domínio de scripting é fundamental para profissionais de segurança que precisam automatizar tarefas repetitivas e criar ferramentas personalizadas. 