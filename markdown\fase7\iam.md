# 7.3 - Gestão de Identidade e Acesso (IAM)

## O que é IAM?
- **Definição**: Conjunto de políticas e tecnologias para garantir que apenas pessoas autorizadas acessem recursos.

## Princípios Fundamentais
- **Menor Privilégio**: Usuários só têm acesso ao necessário.
- **Segregação de Funções**: Separação de responsabilidades para evitar abusos.
- **SSO (Single Sign-On)**: Acesso único a múltiplos sistemas.
- **MFA (Autenticação Multifator)**: Mais de um fator para autenticação.
- **PAM (Privileged Access Management)**: Gestão de acessos privilegiados.

## Boas Práticas
- Revisar acessos periodicamente.
- Implementar MFA sempre que possível.
- Monitorar e registrar acessos privilegiados.
- Automatizar processos de provisionamento e desprovisionamento.

## Conclusão
IAM é essencial para proteger recursos críticos e reduzir riscos de acessos não autorizados. 