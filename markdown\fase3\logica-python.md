# 3.1 - Lógica e Python

## Fundamentos de Lógica de Programação

### Conceitos Básicos

#### Algoritmos
- **Definição**: Sequência finita de instruções para resolver um problema
- **Características**:
  - Entrada: Dados de entrada
  - Processamento: Operações lógicas
  - Saída: Resultado esperado
  - Finitude: Deve terminar
  - Eficiência: Otimização de recursos

#### Variáveis e Tipos de Dados
- **Variável**: Espaço na memória para armazenar dados
- **Tipos Básicos**:
  - **Inteiro**: Números inteiros (1, 2, 3, -1)
  - **Float**: Números deci<PERSON> (3.14, -2.5)
  - **String**: <PERSON>o ("hello", "123")
  - **Boolean**: Verdadeiro ou falso (True, False)

#### Estruturas de Controle
1. **Sequência**: Execução linear de instruções
2. **Seleção**: Decisões condicionais (if/else)
3. **Repetição**: Loops (for, while)

### Lógica Booleana
- **Operadores Lógicos**:
  - AND (&&): Verdadeiro se ambos são verdadeiros
  - OR (||): Verdadeiro se pelo menos um é verdadeiro
  - NOT (!): Inverte o valor booleano
- **Tabelas Verdade**: Mapeamento de todas as combinações possíveis

## Introdução ao Python

### Características da Linguagem
- **Interpretada**: Execução linha por linha
- **Dinamicamente Tipada**: Tipos definidos em tempo de execução
- **Orientada a Objetos**: Suporte a classes e objetos
- **Multiplataforma**: Funciona em diferentes sistemas operacionais

### Sintaxe Básica

#### Variáveis e Atribuição
```python
# Atribuição simples
nome = "João"
idade = 25
altura = 1.75
ativo = True

# Múltiplas atribuições
x, y, z = 1, 2, 3
```

#### Estruturas de Controle

##### Condicionais
```python
# If simples
if idade >= 18:
    print("Maior de idade")

# If-else
if nota >= 7:
    print("Aprovado")
else:
    print("Reprovado")

# If-elif-else
if nota >= 9:
    print("Excelente")
elif nota >= 7:
    print("Bom")
elif nota >= 5:
    print("Regular")
else:
    print("Insuficiente")
```

##### Loops
```python
# For loop
for i in range(5):
    print(i)  # 0, 1, 2, 3, 4

# While loop
contador = 0
while contador < 5:
    print(contador)
    contador += 1
```

### Estruturas de Dados

#### Listas
```python
# Criação
numeros = [1, 2, 3, 4, 5]
frutas = ["maçã", "banana", "laranja"]

# Acesso
primeiro = numeros[0]  # 1
ultimo = numeros[-1]   # 5

# Modificação
numeros.append(6)      # Adiciona no final
numeros.insert(0, 0)   # Insere na posição
numeros.remove(3)      # Remove elemento
```

#### Dicionários
```python
# Criação
pessoa = {
    "nome": "João",
    "idade": 25,
    "cidade": "São Paulo"
}

# Acesso
nome = pessoa["nome"]
idade = pessoa.get("idade", 0)  # Valor padrão se não existir

# Modificação
pessoa["idade"] = 26
pessoa["profissao"] = "Desenvolvedor"
```

#### Tuplas
```python
# Imutáveis
coordenadas = (10, 20)
cores = ("vermelho", "verde", "azul")
```

### Funções

#### Definição Básica
```python
def saudacao(nome):
    return f"Olá, {nome}!"

# Chamada
mensagem = saudacao("João")
print(mensagem)  # Olá, João!
```

#### Parâmetros
```python
# Parâmetros padrão
def potencia(base, expoente=2):
    return base ** expoente

# Parâmetros nomeados
resultado = potencia(base=3, expoente=4)

# Args (argumentos variáveis)
def soma(*numeros):
    return sum(numeros)

total = soma(1, 2, 3, 4, 5)
```

### Tratamento de Exceções
```python
try:
    numero = int(input("Digite um número: "))
    resultado = 10 / numero
    print(f"Resultado: {resultado}")
except ValueError:
    print("Erro: Digite um número válido")
except ZeroDivisionError:
    print("Erro: Divisão por zero")
except Exception as e:
    print(f"Erro inesperado: {e}")
```

## Aplicações em Segurança

### Manipulação de Strings
```python
# Criptografia básica (Cifra de César)
def cifra_cesar(texto, deslocamento):
    resultado = ""
    for char in texto:
        if char.isalpha():
            ascii_offset = 65 if char.isupper() else 97
            novo_char = chr((ord(char) - ascii_offset + deslocamento) % 26 + ascii_offset)
            resultado += novo_char
        else:
            resultado += char
    return resultado

# Exemplo
texto_original = "HELLO"
texto_cifrado = cifra_cesar(texto_original, 3)  # "KHOOR"
```

### Validação de Dados
```python
def validar_email(email):
    import re
    padrao = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(padrao, email) is not None

def validar_senha(senha):
    # Mínimo 8 caracteres, pelo menos uma letra maiúscula, uma minúscula e um número
    if len(senha) < 8:
        return False
    if not any(c.isupper() for c in senha):
        return False
    if not any(c.islower() for c in senha):
        return False
    if not any(c.isdigit() for c in senha):
        return False
    return True
```

### Processamento de Logs
```python
def analisar_logs(arquivo_log):
    tentativas_falha = 0
    ips_suspeitos = {}
    
    with open(arquivo_log, 'r') as arquivo:
        for linha in arquivo:
            if "FAILED LOGIN" in linha:
                tentativas_falha += 1
                # Extrair IP da linha
                partes = linha.split()
                for i, parte in enumerate(partes):
                    if parte == "from":
                        ip = partes[i + 1]
                        ips_suspeitos[ip] = ips_suspeitos.get(ip, 0) + 1
                        break
    
    return tentativas_falha, ips_suspeitos
```

## Bibliotecas para Segurança

### Bibliotecas Essenciais

#### Cryptography
```python
from cryptography.fernet import Fernet

# Geração de chave
chave = Fernet.generate_key()
cipher = Fernet(chave)

# Criptografia
texto = "Dados secretos"
texto_cifrado = cipher.encrypt(texto.encode())
texto_decifrado = cipher.decrypt(texto_cifrado).decode()
```

#### Hashlib
```python
import hashlib

# Hash MD5 (não seguro para senhas)
hash_md5 = hashlib.md5("texto".encode()).hexdigest()

# Hash SHA-256
hash_sha256 = hashlib.sha256("texto".encode()).hexdigest()

# Hash seguro para senhas
import bcrypt
senha = "minhasenha123"
senha_hash = bcrypt.hashpw(senha.encode(), bcrypt.gensalt())
```

#### Requests
```python
import requests

# Requisição HTTP básica
response = requests.get("https://api.exemplo.com/dados")
if response.status_code == 200:
    dados = response.json()

# Com autenticação
headers = {"Authorization": "Bearer token123"}
response = requests.get("https://api.exemplo.com/protegido", headers=headers)
```

### Exemplos Práticos

#### Scanner de Portas Simples
```python
import socket
import threading
import time

def scan_port(ip, porta):
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        resultado = sock.connect_ex((ip, porta))
        sock.close()
        if resultado == 0:
            return porta
    except:
        pass
    return None

def scanner_portas(ip, portas_inicio=1, portas_fim=1024):
    portas_abertas = []
    threads = []
    
    for porta in range(portas_inicio, portas_fim + 1):
        thread = threading.Thread(target=lambda p: portas_abertas.append(scan_port(ip, p)) if scan_port(ip, p) else None, args=(porta,))
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()
    
    return [p for p in portas_abertas if p is not None]
```

#### Validador de Força de Senha
```python
def avaliar_forca_senha(senha):
    pontuacao = 0
    feedback = []
    
    # Comprimento
    if len(senha) >= 8:
        pontuacao += 1
    else:
        feedback.append("Senha deve ter pelo menos 8 caracteres")
    
    # Letras maiúsculas
    if any(c.isupper() for c in senha):
        pontuacao += 1
    else:
        feedback.append("Adicione letras maiúsculas")
    
    # Letras minúsculas
    if any(c.islower() for c in senha):
        pontuacao += 1
    else:
        feedback.append("Adicione letras minúsculas")
    
    # Números
    if any(c.isdigit() for c in senha):
        pontuacao += 1
    else:
        feedback.append("Adicione números")
    
    # Caracteres especiais
    if any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in senha):
        pontuacao += 1
    else:
        feedback.append("Adicione caracteres especiais")
    
    # Classificação
    if pontuacao <= 2:
        classificacao = "Fraca"
    elif pontuacao <= 4:
        classificacao = "Média"
    else:
        classificacao = "Forte"
    
    return {
        "pontuacao": pontuacao,
        "classificacao": classificacao,
        "feedback": feedback
    }
```

## Boas Práticas

### Código Limpo
- **Nomes Descritivos**: Variáveis e funções com nomes claros
- **Comentários**: Documentar lógica complexa
- **Funções Pequenas**: Uma responsabilidade por função
- **Tratamento de Erros**: Sempre tratar exceções

### Segurança
- **Validação de Entrada**: Sempre validar dados de entrada
- **Sanitização**: Limpar dados antes do processamento
- **Princípio do Menor Privilégio**: Acesso mínimo necessário
- **Logs de Segurança**: Registrar atividades importantes

### Performance
- **Algoritmos Eficientes**: Escolher a melhor abordagem
- **Estruturas de Dados**: Usar a estrutura apropriada
- **Profiling**: Medir performance quando necessário
- **Otimização**: Melhorar código crítico

## Conclusão

A lógica de programação e Python fornecem:

- **Base Sólida**: Fundamentos para programação em segurança
- **Ferramentas Poderosas**: Bibliotecas específicas para segurança
- **Flexibilidade**: Adaptação a diferentes cenários
- **Comunidade Ativa**: Suporte e recursos disponíveis

Estes conhecimentos são essenciais para:
- Desenvolvimento de ferramentas de segurança
- Automação de processos de segurança
- Análise de dados de segurança
- Criação de scripts de teste de penetração

O Python continuará sendo uma ferramenta fundamental nas fases avançadas do roadmap. 