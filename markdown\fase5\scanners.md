# 5.2 - Scanners (Teoria)

## O que são Scanners de Vulnerabilidades?
- **Definição**: Ferramentas automatizadas para identificar vulnerabilidades em sistemas, redes e aplicações.
- **Objetivo**: Detectar falhas antes que sejam exploradas por atacantes.

## Principais Scanners

### Nmap
- **Função**: Mapeamento de portas, serviços e detecção de vulnerabilidades básicas.
- **Exemplo de uso**:
```bash
nmap -sV -O alvo.com
nmap --script vuln alvo.com
```

### OpenVAS
- **Função**: Scanner de vulnerabilidades open source, com base de dados atualizada.
- **Recursos**: Relatórios detalhados, integração com SIEM, agendamento de scans.

### Nessus
- **Função**: Scanner comercial, ampla cobertura de vulnerabilidades.
- **Recursos**: Detecção de vulnerabilidades, compliance, relatórios executivos.

### Scanners em Nuvem
- **Exemplos**: <PERSON><PERSON><PERSON>, AWS Inspector, Azure Security Center.
- **Vantagens**: Escalabilidade, atualização automática, integração com cloud.

## Funcionamento dos Scanners
1. **Descoberta de Hosts**: Identificação de dispositivos ativos.
2. **Enumeração de Serviços**: Identificação de portas e serviços abertos.
3. **Detecção de Vulnerabilidades**: Testes automatizados para encontrar falhas conhecidas.
4. **Relatórios**: Geração de relatórios com recomendações de correção.

## Falsos Positivos e Negativos
- **Falso Positivo**: Vulnerabilidade reportada, mas inexistente.
- **Falso Negativo**: Vulnerabilidade real não detectada.
- **Mitigação**: Validação manual, uso de múltiplas ferramentas.

## Boas Práticas
- Realizar scans regulares.
- Validar resultados manualmente.
- Atualizar bases de dados dos scanners.
- Integrar resultados com processos de gestão de vulnerabilidades.

## Conclusão
Scanners são essenciais para identificar e corrigir vulnerabilidades, mas exigem análise crítica dos resultados e integração com processos de segurança. 