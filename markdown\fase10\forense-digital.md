# 10.3 - Forense Digital

## O que é Forense Digital?
- **Definição**: Processo de coleta, preservação, análise e apresentação de evidências digitais.
- **Objetivo**: Investigar incidentes de segurança e crimes cibernéticos.

## Princípios da Forense Digital

### Cadeia de Custódia
- **Definição**: Documentação completa do movimento e localização de evidências.
- **Elementos**: Quem, quando, onde, como as evidências foram coletadas e transferidas.
- **Importância**: Garantir admissibilidade em processos legais.

### Integridade das Evidências
- **Hash**: Verificação de integridade usando algoritmos criptográficos.
- **Write Blockers**: Dispositivos que impedem modificação de evidências.
- **Documentação**: Registro detalhado de todas as ações.

## Tipos de Forense

### Forense de Disco
- **Objetivo**: Análise de dados armazenados em discos.
- **Técnicas**: Imagem forense, análise de slack space, recuperação de arquivos deletados.
- **Ferramentas**: FTK Imager, EnCase, Autopsy.

### Forense de Memória
- **Objetivo**: Análise de dados em memória RAM.
- **Técnicas**: Memory dump, análise de processos, análise de rede.
- **Ferramentas**: Volatility, Rekall, Memoryze.

### Forense de Rede
- **Objetivo**: Análise de tráfego de rede.
- **Técnicas**: Captura de pacotes, análise de logs, reconstrução de sessões.
- **Ferramentas**: Wireshark, tcpdump, NetworkMiner.

### Forense Mobile
- **Objetivo**: Análise de dispositivos móveis.
- **Técnicas**: Extração de dados, análise de aplicações, análise de logs.
- **Ferramentas**: Cellebrite, Oxygen Forensics, XRY.

## Processo Forense

### 1. Identificação
- **Objetivo**: Identificar evidências digitais relevantes.
- **Ações**: Reconhecimento da cena, identificação de dispositivos.

### 2. Preservação
- **Objetivo**: Proteger evidências contra modificação.
- **Ações**: Isolamento de dispositivos, criação de imagens forenses.

### 3. Análise
- **Objetivo**: Examinar evidências para extrair informações relevantes.
- **Ações**: Análise de arquivos, logs, metadados.

### 4. Documentação
- **Objetivo**: Registrar todas as ações e descobertas.
- **Ações**: Relatórios detalhados, capturas de tela, notas.

### 5. Apresentação
- **Objetivo**: Apresentar descobertas de forma clara e compreensível.
- **Ações**: Relatórios executivos, testemunho pericial.

## Ferramentas Forenses

### Análise de Disco
- **FTK Imager**: Criação de imagens forenses.
- **EnCase**: Análise forense avançada.
- **Autopsy**: Ferramenta open source.
- **PhotoRec**: Recuperação de arquivos deletados.

### Análise de Memória
- **Volatility**: Framework para análise de memória.
- **Rekall**: Framework alternativo.
- **Memoryze**: Ferramenta da Mandiant.

### Análise de Rede
- **Wireshark**: Análise de pacotes de rede.
- **NetworkMiner**: Análise de tráfego de rede.
- **Xplico**: Análise de tráfego de rede.

## Boas Práticas
- Sempre usar write blockers.
- Documentar todas as ações.
- Manter cadeia de custódia.
- Usar ferramentas validadas.
- Preservar evidências originais.

## Conclusão
A forense digital é essencial para investigar incidentes de segurança e crimes cibernéticos, fornecendo evidências confiáveis para processos legais. 