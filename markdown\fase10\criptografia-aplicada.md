# 10.1 - Criptografia Aplicada

## Criptografia Simétrica vs. Assimétrica

### Criptografia Simétrica
- **Definição**: Usa a mesma chave para criptografar e descriptografar.
- **Vantagens**: Rápida, eficiente para grandes volumes de dados.
- **Desvantagens**: Problema de distribuição de chaves.
- **Exemplos**: AES, DES, 3DES.

### Criptografia Assimétrica
- **Definição**: Usa par de chaves (pública e privada).
- **Vantagens**: Resolve problema de distribuição de chaves.
- **Desvantagens**: Mais lenta, chaves maiores.
- **Exemplos**: RSA, ECC, DSA.

## Hashing

### O que é Hashing?
- **Definição**: Função matemática que converte dados de qualquer tamanho em string de tamanho fixo.
- **Características**: Unidirecional, determinístico, resistente a colisões.

### Algoritmos de Hash
- **MD5**: O<PERSON>oleto, não seguro.
- **SHA-1**: Obsoleto, não seguro.
- **SHA-256**: Amplamente usado, seguro.
- **SHA-3**: Nova família de algoritmos.

### Aplicações do Hashing
- **Verificação de Integridade**: Detectar modificações em arquivos.
- **Armazenamento de Senhas**: Hash + salt para segurança.
- **Assinaturas Digitais**: Hash + criptografia assimétrica.

## Assinaturas Digitais

### Como Funcionam
1. **Hash**: Gerar hash do documento.
2. **Criptografia**: Criptografar hash com chave privada.
3. **Verificação**: Descriptografar com chave pública e comparar hashes.

### Propriedades
- **Autenticidade**: Garante origem do documento.
- **Integridade**: Garante que documento não foi alterado.
- **Não-repúdio**: Impede negação de autoria.

## TLS/SSL

### O que é TLS?
- **Definição**: Protocolo de segurança para comunicação na internet.
- **Objetivo**: Criptografar comunicação entre cliente e servidor.

### Handshake TLS
1. **Client Hello**: Cliente propõe parâmetros de criptografia.
2. **Server Hello**: Servidor escolhe parâmetros.
3. **Troca de Chaves**: Negociação de chave de sessão.
4. **Criptografia**: Comunicação criptografada.

### Certificados SSL/TLS
- **Certificado**: Documento digital que vincula chave pública a identidade.
- **CA (Certificate Authority)**: Entidade que emite certificados.
- **Validação**: Verificação da identidade do certificado.

## Aplicações Práticas

### Criptografia de Dados em Repouso
- **Full Disk Encryption**: Criptografia de disco inteiro.
- **File Encryption**: Criptografia de arquivos específicos.
- **Database Encryption**: Criptografia de bancos de dados.

### Criptografia de Dados em Trânsito
- **HTTPS**: Criptografia de comunicação web.
- **VPN**: Túnel criptografado para comunicação remota.
- **Email Encryption**: Criptografia de emails.

### Gestão de Chaves
- **Key Management**: Processos para gerar, armazenar, distribuir e destruir chaves.
- **HSM (Hardware Security Module)**: Dispositivo físico para gestão segura de chaves.
- **Key Rotation**: Troca periódica de chaves.

## Boas Práticas
- Usar algoritmos criptográficos atualizados.
- Implementar gestão adequada de chaves.
- Validar certificados SSL/TLS.
- Monitorar expiração de certificados.
- Fazer backup seguro de chaves.

## Conclusão
A criptografia aplicada é fundamental para proteger dados em repouso e em trânsito, garantindo confidencialidade, integridade e autenticidade. 