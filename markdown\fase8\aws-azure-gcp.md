# 8.2 - Seguran<PERSON> em AWS, Azure e GCP

## AWS (Amazon Web Services)

### IAM (Identity and Access Management)
- **Usuários**: Contas para pessoas ou aplicações.
- **Grupos**: Coleções de usuários com permissões similares.
- **Roles**: Permissões temporárias para recursos.
- **Políticas**: Documentos JSON que definem permissões.

### S3 (Simple Storage Service)
- **Buckets**: Containers para armazenar objetos.
- **ACLs**: Controle de acesso básico.
- **Bucket Policies**: Políticas avançadas de acesso.
- **Criptografia**: SSE-S3, SSE-KMS, SSE-C.

### VPC (Virtual Private Cloud)
- **Subnets**: Segmentação de rede.
- **Security Groups**: Firewalls no nível de instância.
- **NACLs**: Firewalls no nível de subnet.
- **Internet Gateway**: Conectividade com internet.

## Azure

### Azure Active Directory
- **Tenants**: Organizações no Azure AD.
- **Users**: Contas de usuário.
- **Groups**: Coleções de usuários.
- **Conditional Access**: Políticas baseadas em condições.

### Azure Security Center
- **Security Posture**: Avaliação de segurança.
- **Threat Protection**: Detecção de ameaças.
- **Security Recommendations**: Recomendações de segurança.

### Azure Key Vault
- **Secrets**: Armazenamento seguro de segredos.
- **Keys**: Gerenciamento de chaves criptográficas.
- **Certificates**: Certificados SSL/TLS.

## Google Cloud Platform (GCP)

### Cloud IAM
- **Principals**: Usuários, grupos, service accounts.
- **Roles**: Conjuntos de permissões.
- **Policies**: Atribuições de roles a principals.

### Cloud Security Command Center
- **Asset Inventory**: Inventário de ativos.
- **Threat Detection**: Detecção de ameaças.
- **Vulnerability Scanning**: Varredura de vulnerabilidades.

### Cloud KMS
- **Key Management**: Gerenciamento de chaves.
- **Encryption**: Criptografia de dados.
- **Digital Signatures**: Assinaturas digitais.

## Boas Práticas Comuns
- Implementar princípio do menor privilégio.
- Usar autenticação multifator.
- Criptografar dados em repouso e em trânsito.
- Monitorar logs e atividades.
- Fazer backups regulares.
- Testar planos de recuperação.

## Conclusão
Cada provedor cloud tem suas particularidades de segurança, mas os princípios fundamentais são similares: controle de acesso, criptografia, monitoramento e backup. 