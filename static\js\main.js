// CYPHER Learning System - Main JavaScript

class CypherApp {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeComponents();
    }

    setupEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            console.log('CYPHER System Initialized');
            this.initializeIcons();
        });
    }

    initializeComponents() {
        // Initialize collapsible components
        this.initCollapsibles();
        
        // Initialize time updates if on estudos page
        if (window.location.pathname === '/estudos') {
            this.initTimeUpdates();
            this.initConnectionStatus();
        }
    }

    initializeIcons() {
        // Lucide icons will be initialized by the script tag in base.html
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    // Collapsible functionality
    initCollapsibles() {
        const collapsibleTriggers = document.querySelectorAll('[data-collapsible-trigger]');
        
        collapsibleTriggers.forEach(trigger => {
            trigger.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = trigger.getAttribute('data-target');
                const content = document.getElementById(targetId);
                const chevron = trigger.querySelector('[data-chevron]');
                
                if (content) {
                    const isOpen = content.style.display !== 'none';
                    
                    if (isOpen) {
                        content.style.display = 'none';
                        trigger.setAttribute('aria-expanded', 'false');
                        if (chevron) {
                            chevron.setAttribute('data-lucide', 'chevron-right');
                        }
                    } else {
                        content.style.display = 'block';
                        trigger.setAttribute('aria-expanded', 'true');
                        if (chevron) {
                            chevron.setAttribute('data-lucide', 'chevron-down');
                        }
                    }
                    
                    // Reinitialize icons after changing them
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons();
                    }
                }
            });
        });
    }

    // Time updates for estudos page
    initTimeUpdates() {
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            this.updateTime();
            setInterval(() => this.updateTime(), 1000);
        }
    }

    updateTime() {
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            const now = new Date();
            timeElement.textContent = now.toLocaleTimeString();
        }
    }

    // Connection status simulation
    initConnectionStatus() {
        const statusElement = document.getElementById('connection-status');
        const iconElement = document.getElementById('connection-icon');
        
        if (statusElement && iconElement) {
            this.updateConnectionStatus();
            setInterval(() => this.updateConnectionStatus(), 8000);
        }
    }

    updateConnectionStatus() {
        const statusElement = document.getElementById('connection-status');
        const iconElement = document.getElementById('connection-icon');
        
        if (statusElement && iconElement) {
            const isConnected = Math.random() > 0.5;
            
            if (isConnected) {
                statusElement.textContent = 'CONNECTED';
                statusElement.className = 'text-white font-mono text-sm';
                iconElement.setAttribute('data-lucide', 'wifi');
                iconElement.className = 'w-4 h-4 text-white';
            } else {
                statusElement.textContent = 'RECONNECTING...';
                statusElement.className = 'text-gray-600 font-mono text-sm';
                iconElement.setAttribute('data-lucide', 'wifi-off');
                iconElement.className = 'w-4 h-4 text-gray-600';
            }
            
            // Reinitialize icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }
    }

    // Button component functionality
    static createButton(text, variant = 'default', onClick = null) {
        const button = document.createElement('button');
        button.textContent = text;
        
        const baseClasses = 'transition-colors font-medium text-sm h-10 px-4 rounded';
        let variantClasses = '';
        
        switch (variant) {
            case 'ghost':
                variantClasses = 'hover:bg-gray-800 hover:text-white';
                break;
            case 'primary':
                variantClasses = 'bg-white text-black hover:bg-gray-200';
                break;
            default:
                variantClasses = 'bg-gray-800 text-white hover:bg-gray-700';
        }
        
        button.className = `${baseClasses} ${variantClasses}`;
        
        if (onClick) {
            button.addEventListener('click', onClick);
        }
        
        return button;
    }

    // Utility functions
    static addClass(element, className) {
        if (element && !element.classList.contains(className)) {
            element.classList.add(className);
        }
    }

    static removeClass(element, className) {
        if (element && element.classList.contains(className)) {
            element.classList.remove(className);
        }
    }

    static toggleClass(element, className) {
        if (element) {
            element.classList.toggle(className);
        }
    }

    // API calls
    static async fetchTime() {
        try {
            const response = await fetch('/api/time');
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error fetching time:', error);
            return null;
        }
    }

    static async fetchConnectionStatus() {
        try {
            const response = await fetch('/api/connection-status');
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error fetching connection status:', error);
            return null;
        }
    }



// (Removido o sistema de substituição de emojis por SVG outline)

// Matrix rain effect for background
window.addEventListener('DOMContentLoaded', () => {
  const canvas = document.getElementById('matrix-canvas');
  if (!canvas) return;
  const ctx = canvas.getContext('2d');
  let width = window.innerWidth;
  let height = window.innerHeight;
  canvas.width = width;
  canvas.height = height;

  // Array de gotas individuais
  let drops = [];
  let waveTimer = 0;
  const waveInterval = 5000; // Intervalo entre levas (5 segundos)

  // Função para criar uma nova gota
  const createDrop = (id) => ({
    id,
    x: Math.random() * width,
    y: -Math.random() * 200 - 100, // Posição inicial variada acima da tela
    speed: Math.random() * 1.2 + 0.8,
    chars: Array.from({ length: 15 }, () => (Math.random() > 0.5 ? "1" : "0")).join(""),
    depth: Math.random() * 0.6 + 0.4, // Profundidade entre 0.4 e 1.0
  });

  // Função para criar uma nova leva de gotas
  const createNewWave = () => {
    const newDrops = Array.from({ length: 20 }, (_, i) => createDrop(`wave_${Date.now()}_${i}`));
    drops.push(...newDrops);
  };

  // Inicializar gotas
  const initializeDrops = () => {
    drops = Array.from({ length: 120 }, (_, i) => createDrop(i));
  };

  // Função de desenho
  function draw() {
    // Limpar o canvas completamente
    ctx.clearRect(0, 0, width, height);
    ctx.font = '14px monospace';
    ctx.fillStyle = '#ffffff';
    
    // Gerenciar levas de gotas
    waveTimer += 50; // Incrementar timer (50ms por frame)
    if (waveTimer >= waveInterval) {
      createNewWave();
      waveTimer = 0;
    }
    
    drops.forEach((drop) => {
      // Desenhar cada caractere da sequência verticalmente
      drop.chars.split("").forEach((char, index) => {
        const x = drop.x;
        const y = drop.y + (index * 14);
        
        // Desenhar se o caractere estiver visível na tela (com margem maior)
        if (y > -50 && y < height + 100) {
          // Aplicar fade baseado na posição do caractere e profundidade
          const fadeOpacity = Math.max(0, 1 - index * 0.08);
          const depthOpacity = drop.depth * 0.6; // Opacidade baseada na profundidade
          const finalOpacity = fadeOpacity * depthOpacity;
          
          ctx.globalAlpha = finalOpacity;
          ctx.fillText(char, x, y);
        }
      });
      
      // Resetar opacidade
      ctx.globalAlpha = 1;
      
      // Atualizar posição da gota
      drop.y += drop.speed;
      
      // Recriar gota quando toda a sequência sair da tela
      // Considerando que cada caractere tem 14px de altura e há 15 caracteres
      const totalSequenceHeight = 15 * 14; // 210px de altura total da sequência
      if (drop.y > height + totalSequenceHeight + 50) {
        Object.assign(drop, createDrop(drop.id));
      }
      
      // Ocasionalmente mudar os caracteres
      if (Math.random() > 0.98) {
        drop.chars = Array.from({ length: 15 }, () => (Math.random() > 0.5 ? "1" : "0")).join("");
        drop.depth = Math.random() * 0.6 + 0.4; // Atualizar profundidade também
      }
    });
    
    // Limitar o número total de gotas para performance
    if (drops.length > 200) {
      drops = drops.slice(-150);
    }
  }

  // Inicializar gotas
  initializeDrops();

  // Loop de animação
  setInterval(draw, 50);

  // Resize handler
  window.addEventListener('resize', () => {
    width = window.innerWidth;
    height = window.innerHeight;
    canvas.width = width;
    canvas.height = height;
    initializeDrops(); // Recriar gotas com novas dimensões
  });
});

// Sidebar toggle functionality
window.addEventListener('DOMContentLoaded', () => {
  const sidebar = document.getElementById('study-sidebar');
  const toggleBtn = document.getElementById('toggle-sidebar');
  const showSidebarBtn = document.getElementById('show-sidebar-btn');
  if (sidebar && toggleBtn && showSidebarBtn) {
    let hidden = false;
    toggleBtn.addEventListener('click', () => {
      hidden = true;
      sidebar.style.width = '0';
      sidebar.style.minWidth = '0';
      sidebar.style.overflow = 'hidden';
      sidebar.classList.add('collapsed');
      showSidebarBtn.style.display = 'block';
    });
    showSidebarBtn.querySelector('button').addEventListener('click', () => {
      hidden = false;
      sidebar.style.width = '';
      sidebar.style.minWidth = '';
      sidebar.style.overflow = '';
      sidebar.classList.remove('collapsed');
      showSidebarBtn.style.display = 'none';
    });
  }
});

// Initialize the app
const cypherApp = new CypherApp();

// Export for use in other scripts
window.CypherApp = CypherApp;
