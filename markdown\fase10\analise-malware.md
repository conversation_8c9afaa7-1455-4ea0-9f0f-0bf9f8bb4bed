# 10.2 - An<PERSON><PERSON><PERSON> de Malware

## O que é Malware?
- **Definição**: Software malicioso projetado para causar danos ou obter acesso não autorizado.
- **Tipos**: Vírus, worms, trojans, ransomware, spyware, adware.

## An<PERSON><PERSON><PERSON> E<PERSON>á<PERSON> vs. Dinâmica

### Análise Estática
- **Definição**: Análise do código sem executá-lo.
- **Técnicas**: Análise de strings, hex dump, disassembly, análise de PE headers.
- **Vantagens**: Segura, não requer execução.
- **Desvantagens**: Pode não revelar comportamento completo.

### Análise Dinâmic<PERSON>
- **Definição**: Análise do comportamento durante execução.
- **Técnicas**: Sandboxing, monitoramento de sistema, análise de rede.
- **Vantagens**: Revela comportamento real.
- **Desvantagens**: <PERSON><PERSON> de infecção, pode não executar.

## Tipos de Malware

### Ransomware
- **Definição**: Malware que criptografa arquivos e exige resgate.
- **Exemplos**: WannaCry, NotPetya, Ryuk.
- **Prevenção**: Backup regular, patches de segurança, treinamento.

### Fileless Malware
- **Definição**: Malware que não deixa arquivos no disco.
- **Técnicas**: Injeção de código, PowerShell, WMI.
- **Detecção**: Monitoramento de memória, análise de logs.

### Rootkits
- **Definição**: Malware que esconde sua presença no sistema.
- **Técnicas**: Kernel-level hooks, API hooking.
- **Detecção**: Análise de integridade, memory forensics.

## Ferramentas de Análise

### Análise Estática
- **PE Explorer**: Análise de arquivos PE.
- **IDA Pro**: Disassembly avançado.
- **Ghidra**: Ferramenta gratuita da NSA.
- **Strings**: Extração de strings de arquivos.

### Análise Dinâmica
- **Cuckoo Sandbox**: Sandbox automatizado.
- **Process Monitor**: Monitoramento de sistema.
- **Wireshark**: Análise de tráfego de rede.
- **Volatility**: Análise de memória.

### Análise de Rede
- **FakeNet**: Simulação de rede para malware.
- **INetSim**: Simulação de serviços de internet.
- **Wireshark**: Captura e análise de pacotes.

## Técnicas de Análise

### Análise de Strings
- **Objetivo**: Extrair strings legíveis do malware.
- **Técnicas**: ASCII, Unicode, encoded strings.
- **Ferramentas**: Strings, PE Explorer.

### Análise de PE Headers
- **Imports**: DLLs e funções importadas.
- **Exports**: Funções exportadas.
- **Sections**: Seções do executável.
- **Resources**: Recursos embutidos.

### Análise de Comportamento
- **File System**: Arquivos criados, modificados, deletados.
- **Registry**: Chaves de registro modificadas.
- **Network**: Conexões de rede estabelecidas.
- **Processes**: Processos criados ou injetados.

## Assinaturas de Malware

### O que são Assinaturas?
- **Definição**: Padrões únicos que identificam malware específico.
- **Tipos**: String signatures, hash signatures, behavioral signatures.

### Criação de Assinaturas
- **Identificação**: Encontrar padrões únicos no malware.
- **Validação**: Testar contra amostras limpas.
- **Distribuição**: Compartilhar com ferramentas de detecção.

## Boas Práticas
- Usar ambiente isolado para análise.
- Documentar todas as descobertas.
- Manter ferramentas atualizadas.
- Compartilhar descobertas com a comunidade.
- Seguir procedimentos de segurança.

## Conclusão
A análise de malware é essencial para entender ameaças e desenvolver controles de segurança adequados. 