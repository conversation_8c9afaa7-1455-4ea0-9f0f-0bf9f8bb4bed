# 9.2 - An<PERSON><PERSON><PERSON> de Ameaças e Resposta a Incidentes

## Threat Hunting

### O que é Threat Hunting?
- **Definição**: Processo proativo de busca por ameaças que não foram detectadas por ferramentas automatizadas.
- **Objetivo**: Encontrar e neutralizar ameaças antes que causem danos.

### Metodologias de Threat Hunting
- **Hypothesis-Driven**: Baseado em hipóteses sobre possíveis ameaças.
- **Intel-Driven**: Baseado em indicadores de comprometimento (IOCs).
- **TTP-Driven**: Baseado em técnicas, táticas e procedimentos conhecidos.

### Ferramentas de Threat Hunting
- **SIEM**: Correlação de eventos e logs.
- **EDR**: Endpoint Detection and Response.
- **Network Monitoring**: Análise de tráfego de rede.
- **Memory Analysis**: Análise de memória de sistemas.

## MITRE ATT&CK

### O que é MITRE ATT&CK?
- **Definição**: Framework que mapeia táticas e técnicas de atacantes.
- **Objetivo**: Compreender e defender contra ameaças conhecidas.

### Estrutura do ATT&CK
- **Tactics**: Objetivos dos atacantes (Reconnaissance, Initial Access, etc.).
- **Techniques**: Métodos específicos para alcançar objetivos.
- **Sub-techniques**: Variações de técnicas.
- **Procedures**: Exemplos reais de uso das técnicas.

### Aplicações do ATT&CK
- **Threat Intelligence**: Análise de ameaças conhecidas.
- **Detection Engineering**: Desenvolvimento de regras de detecção.
- **Adversary Emulation**: Simulação de ataques conhecidos.
- **Assessment**: Avaliação de capacidades defensivas.

## IOCs (Indicators of Compromise)

### Tipos de IOCs
- **IP Addresses**: Endereços IP maliciosos.
- **Domain Names**: Domínios usados por atacantes.
- **File Hashes**: Hashes de arquivos maliciosos.
- **Email Addresses**: Emails de phishing.
- **Registry Keys**: Chaves de registro modificadas.

### Gestão de IOCs
- **Coleta**: Obtenção de IOCs de fontes confiáveis.
- **Validação**: Verificação da qualidade dos IOCs.
- **Distribuição**: Compartilhamento com equipes de segurança.
- **Expiração**: Remoção de IOCs obsoletos.

## Playbooks de Resposta

### Estrutura de um Playbook
- **Trigger**: Condição que ativa o playbook.
- **Investigation**: Passos para investigar o incidente.
- **Containment**: Ações para conter a ameaça.
- **Eradication**: Remoção da ameaça.
- **Recovery**: Restauração de sistemas.
- **Lessons Learned**: Documentação de lições aprendidas.

### Exemplos de Playbooks
- **Malware Detection**: Resposta a detecção de malware.
- **Phishing Incident**: Resposta a campanhas de phishing.
- **Data Breach**: Resposta a vazamento de dados.
- **Ransomware**: Resposta a ataques de ransomware.

## Boas Práticas
- Manter playbooks atualizados.
- Treinar equipe regularmente.
- Documentar todos os incidentes.
- Realizar exercícios de resposta.
- Integrar com ferramentas de segurança.

## Conclusão
Análise de ameaças e resposta a incidentes são habilidades críticas para proteger organizações contra ataques cibernéticos. 