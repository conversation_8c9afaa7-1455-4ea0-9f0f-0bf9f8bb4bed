# 7.2 - Segurança em Linux e Windows

## Hardening de Sistemas
- **Definição**: Processo de reforço da segurança de sistemas operacionais.
- **Ações**: Remover serviços desnecessários, aplicar patches, configurar permissões.

## Logs e Auditoria
- **Logs**: Registro de eventos do sistema (auth.log, event viewer).
- **Auditoria**: Monitoramento de ações suspeitas e análise de incidentes.

## Active Directory e GPOs
- **Active Directory**: Gerenciamento centralizado de usuários e permissões no Windows.
- **GPOs**: Políticas de grupo para controle de configurações e segurança.

## Boas Práticas
- Manter sistemas atualizados.
- Restringir privilégios de usuários.
- Monitorar logs regularmente.
- Utilizar autenticação multifator.

## Conclusão
A segurança de sistemas operacionais é fundamental para evitar ataques e manter a integridade dos dados. 