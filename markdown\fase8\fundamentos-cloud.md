# 8.1 - Fundamentos Cloud

## O que é Cloud Computing?
- **Definição**: Modelo de computação que oferece recursos de TI como serviços via internet.
- **Características**: On-demand, broad network access, resource pooling, rapid elasticity, measured service.

## Modelos de Serviço

### IaaS (Infrastructure as a Service)
- **Definição**: Infraestrutura como serviço (servidores, storage, networking).
- **Exemplos**: AWS EC2, Azure Virtual Machines, Google Compute Engine.
- **Responsabilidades**: Cliente gerencia OS, aplicações, dados; provedor gerencia hardware.

### PaaS (Platform as a Service)
- **Definição**: Plataforma como serviço para desenvolvimento e deploy de aplicações.
- **Exemplos**: AWS Elastic Beanstalk, Azure App Service, Google App Engine.
- **Responsabilidades**: Cliente gerencia aplicações e dados; provedor gerencia infraestrutura.

### SaaS (Software as a Service)
- **Definição**: Software como serviço acessível via navegador.
- **Exemplos**: Salesforce, Office 365, Google Workspace.
- **Responsabilidades**: Cliente usa aplicação; provedor gerencia tudo.

## Shared Responsibility Model
- **Conceito**: Divisão de responsabilidades de segurança entre cliente e provedor.
- **Cliente**: Dados, aplicações, configurações de segurança.
- **Provedor**: Infraestrutura física, virtualização, rede.

## Compliance e Regulamentações
- **GDPR**: Proteção de dados pessoais na UE.
- **LGPD**: Lei Geral de Proteção de Dados no Brasil.
- **SOX**: Controles financeiros e de TI.
- **HIPAA**: Proteção de dados de saúde.

## Boas Práticas de Segurança Cloud
- Implementar autenticação multifator.
- Usar criptografia para dados em repouso e em trânsito.
- Configurar corretamente grupos de segurança e firewalls.
- Monitorar logs e atividades suspeitas.
- Fazer backups regulares e testar recuperação.

## Conclusão
Compreender os fundamentos de cloud é essencial para implementar controles de segurança adequados e aproveitar os benefícios da computação em nuvem. 