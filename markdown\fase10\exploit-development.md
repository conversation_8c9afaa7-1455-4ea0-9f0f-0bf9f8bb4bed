# 10.5 - Exploit Development

## O que é Exploit Development?
- **Definição**: Processo de criar código que explora vulnerabilidades em software.
- **Objetivo**: Compreender vulnerabilidades para desenvolver proteções.

## Assembly Básico

### Arquiteturas
- **x86**: Arquitetura de 32 bits.
- **x64**: Arquitetura de 64 bits.
- **ARM**: Arquitetura usada em dispositivos móveis.

### Registradores
- **EAX, EBX, ECX, EDX**: Registradores de propósito geral.
- **ESP**: Stack pointer.
- **EBP**: Base pointer.
- **EIP**: Instruction pointer.

### Instruções Básicas
- **MOV**: Mover dados entre registradores.
- **PUSH/POP**: Operações de stack.
- **CALL/RET**: Chamadas de função.
- **JMP**: Saltos incondicionais.

## Buffer Overflows

### Stack Overflow
- **Definição**: Sobrescrever dados na stack.
- **Causa**: Falta de validação de tamanho de entrada.
- **Exploit**: Sobrescrever endereço de retorno.

### Heap Overflow
- **Definição**: Corrupção de dados no heap.
- **Causa**: Falta de validação em alocação dinâmica.
- **Exploit**: Corrupção de estruturas de dados.

### Prevention
- **ASLR**: Address Space Layout Randomization.
- **DEP**: Data Execution Prevention.
- **Stack Canaries**: Valores de proteção na stack.

## ROP (Return-Oriented Programming)

### O que é ROP?
- **Definição**: Técnica que usa gadgets (pequenos trechos de código) existentes.
- **Objetivo**: Contornar proteções como DEP.

### Gadgets
- **Definição**: Sequências de instruções terminadas em RET.
- **Uso**: Construir cadeias de execução.
- **Ferramentas**: ROPgadget, ropper.

## Format String Bugs

### O que são?
- **Definição**: Vulnerabilidade em funções de formatação (printf, sprintf).
- **Causa**: Uso de string de formato controlada pelo usuário.

### Exploits
- **Leitura de Memória**: Usar %x para ler valores da stack.
- **Escrita de Memória**: Usar %n para escrever em endereços.
- **Overwrite**: Sobrescrever endereços importantes.

## Ferramentas de Desenvolvimento

### Debuggers
- **GDB**: Debugger do GNU.
- **WinDbg**: Debugger da Microsoft.
- **x64dbg**: Debugger open source para Windows.

### Disassemblers
- **IDA Pro**: Disassembler avançado.
- **Ghidra**: Ferramenta gratuita da NSA.
- **Radare2**: Framework de análise binária.

### Fuzzers
- **AFL**: American Fuzzy Lop.
- **Peach**: Framework de fuzzing.
- **Spike**: Fuzzer para protocolos de rede.

## Boas Práticas

### Para Pesquisadores
- Usar ambientes isolados.
- Documentar vulnerabilidades encontradas.
- Reportar bugs responsavelmente.
- Seguir ética de pesquisa.

### Para Desenvolvedores
- Implementar validação de entrada.
- Usar ferramentas de análise estática.
- Fazer testes de segurança.
- Manter dependências atualizadas.

## Conclusão
Exploit development é essencial para compreender vulnerabilidades e desenvolver proteções adequadas, mas deve ser feito de forma ética e responsável. 