# 4.2 - Técnicas e Ferramentas (Teoria)

## Google Dorking

### Conceito e Aplicações
- **Definição**: Uso avançado de operadores de pesquisa do Google
- **Objetivos**:
  - Encontrar informações sensíveis expostas
  - Descobrir arquivos e diretórios
  - Identificar vulnerabilidades
  - Coletar informações de configuração

### Operadores Básicos
```bash
# Pesquisa em site específico
site:exemplo.com

# Pesquisa por tipo de arquivo
filetype:pdf
filetype:doc
filetype:xls

# Pesquisa por extensão
ext:pdf
ext:conf
ext:log

# Pesquisa por intitle
intitle:"index of"
intitle:"admin"
intitle:"login"
```

### Operadores Avançados
```bash
# Pesquisa por inurl
inurl:admin
inurl:login
inurl:config

# Pesquisa por intext
intext:"password"
intext:"username"
intext:"error"

# Combinações
site:exemplo.com filetype:pdf
site:exemplo.com inurl:admin
site:exemplo.com intitle:"index of"
```

### Dorks Específicos para Segurança
```bash
# Configurações de banco de dados
inurl:config.php
inurl:wp-config.php
inurl:database.php

# Logs de erro
intext:"error" filetype:log
intext:"warning" filetype:log

# Backups
filetype:bak
filetype:backup
inurl:backup

# Painéis administrativos
intitle:"admin panel"
intitle:"administrator"
inurl:admin/login
```

## Whois e Informações de Domínio

### Consultas Whois
```bash
# Consulta básica
whois exemplo.com

# Consulta específica de servidor
whois -h whois.verisign-grs.com exemplo.com

# Consulta de IP
whois ***********

# Consulta de ASN
whois -h whois.radb.net "!gAS12345"
```

### Informações Extraídas
- **Registrant**: Informações do registrante
- **Administrative Contact**: Contato administrativo
- **Technical Contact**: Contato técnico
- **Name Servers**: Servidores DNS
- **Creation Date**: Data de criação
- **Expiration Date**: Data de expiração

### Ferramentas de Análise
```bash
# Whois com formatação
whois exemplo.com | grep -E "(Registrant|Admin|Tech|Name Server)"

# Extrair emails
whois exemplo.com | grep -oE "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"

# Extrair telefones
whois exemplo.com | grep -oE "[\+]?[0-9]{1,4}[-\s]?[0-9]{1,4}[-\s]?[0-9]{1,4}"
```

## DNS e Enumeração

### Consultas DNS Básicas
```bash
# Consulta A record
dig exemplo.com A

# Consulta MX record
dig exemplo.com MX

# Consulta NS record
dig exemplo.com NS

# Consulta TXT record
dig exemplo.com TXT

# Consulta CNAME
dig www.exemplo.com CNAME
```

### Técnicas Avançadas de DNS
```bash
# Zone transfer (se permitida)
dig @ns1.exemplo.com exemplo.com AXFR

# Consulta reversa
dig -x ***********

# Consulta ANY
dig exemplo.com ANY

# Consulta com servidor específico
dig @8.8.8.8 exemplo.com
```

### Enumeração de Subdomínios
```bash
# Sublist3r
python sublist3r.py -d exemplo.com

# Amass
amass enum -d exemplo.com

# Subfinder
subfinder -d exemplo.com -o subdomains.txt

# Gobuster (DNS)
gobuster dns -d exemplo.com -w wordlist.txt
```

## Shodan e IoT

### Conceito do Shodan
- **Definição**: Motor de busca para dispositivos conectados à internet
- **Capacidades**:
  - Busca por serviços e portas
  - Identificação de dispositivos IoT
  - Descoberta de vulnerabilidades
  - Mapeamento de infraestrutura

### Consultas Shodan
```bash
# Busca por hostname
shodan search hostname:exemplo.com

# Busca por organização
shodan search org:"Empresa"

# Busca por produto
shodan search product:"Apache"

# Busca por porta
shodan search port:80

# Busca por país
shodan search country:"BR"
```

### Filtros Avançados
```bash
# Busca por SSL
shodan search ssl:"exemplo.com"

# Busca por certificado
shodan search ssl.cert.subject.CN:"exemplo.com"

# Busca por banner
shodan search banner:"SSH"

# Busca por versão
shodan search product:"Apache" version:"2.4"
```

### Análise de Resultados
```bash
# Extrair IPs
shodan search hostname:exemplo.com | grep -oE "\b([0-9]{1,3}\.){3}[0-9]{1,3}\b"

# Extrair portas
shodan search hostname:exemplo.com | grep -o "port:[0-9]*"

# Extrair produtos
shodan search hostname:exemplo.com | grep -o "product:[^,]*"
```

## TheHarvester

### Instalação e Configuração
```bash
# Instalação via pip
pip install theHarvester

# Instalação via git
git clone https://github.com/laranjas/theHarvester.git
cd theHarvester
pip install -r requirements.txt
```

### Uso Básico
```bash
# Busca básica
theHarvester -d exemplo.com -b google

# Busca em múltiplas fontes
theHarvester -d exemplo.com -b all

# Limitar resultados
theHarvester -d exemplo.com -b google -l 100

# Salvar em arquivo
theHarvester -d exemplo.com -b all -f resultados.txt
```

### Fontes Disponíveis
```bash
# Lista de fontes
theHarvester -s

# Fontes populares
-b google      # Google
-b bing        # Bing
-b linkedin    # LinkedIn
-b twitter     # Twitter
-b github      # GitHub
-b shodan      # Shodan
-b virustotal  # VirusTotal
```

### Análise de Resultados
```bash
# Extrair emails
theHarvester -d exemplo.com -b all | grep -oE "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"

# Extrair subdomínios
theHarvester -d exemplo.com -b all | grep -oE "[a-zA-Z0-9.-]+\.exemplo\.com"

# Extrair IPs
theHarvester -d exemplo.com -b all | grep -oE "\b([0-9]{1,3}\.){3}[0-9]{1,3}\b"
```

## Maltego

### Conceito e Aplicações
- **Definição**: Ferramenta de análise de relações e mapeamento
- **Capacidades**:
  - Mapeamento visual de relações
  - Análise de redes sociais
  - Descoberta de conexões
  - Geração de relatórios

### Transformações Disponíveis
```bash
# Transformações de domínio
DomainToDNSName
DomainToIPAddress
DomainToMXRecord
DomainToNSRecord

# Transformações de email
EmailToDomain
EmailToPerson
EmailToPhoneNumber

# Transformações de pessoa
PersonToEmail
PersonToPhoneNumber
PersonToSocialNetwork
```

### Casos de Uso
```bash
# Análise de domínio
1. Adicionar entidade Domain
2. Executar DomainToDNSName
3. Executar DomainToIPAddress
4. Executar DomainToMXRecord

# Análise de pessoa
1. Adicionar entidade Person
2. Executar PersonToEmail
3. Executar PersonToSocialNetwork
4. Executar PersonToPhoneNumber
```

## Recon-ng

### Instalação e Configuração
```bash
# Clonar repositório
git clone https://github.com/lanmaster53/recon-ng.git
cd recon-ng

# Instalar dependências
pip install -r REQUIREMENTS

# Executar
./recon-ng
```

### Módulos Disponíveis
```bash
# Módulos de enumeração
recon/profiles-profiles/namechk
recon/profiles-profiles/profiler
recon/profiles-profiles/username

# Módulos de pesquisa
recon/companies-multi/whois_pocs
recon/companies-multi/crunchbase
recon/companies-multi/jigsaw

# Módulos de redes sociais
recon/profiles-profiles/twitter
recon/profiles-profiles/linkedin
recon/profiles-profiles/facebook
```

### Workflow Básico
```bash
# Iniciar recon-ng
recon-ng

# Adicionar workspace
workspaces add exemplo

# Usar módulo
use recon/profiles-profiles/namechk

# Configurar parâmetros
set SOURCE exemplo.com

# Executar
run

# Ver resultados
show hosts
show contacts
```

## Técnicas de Pesquisa Avançada

### Pesquisa em Redes Sociais
```bash
# LinkedIn
site:linkedin.com "Empresa"
site:linkedin.com "exemplo.com"

# Twitter
site:twitter.com "Empresa"
site:twitter.com "@exemplo"

# Facebook
site:facebook.com "Empresa"
site:facebook.com "exemplo.com"
```

### Pesquisa de Documentos
```bash
# PDFs
site:exemplo.com filetype:pdf
filetype:pdf "Empresa"

# Apresentações
site:exemplo.com filetype:ppt
site:exemplo.com filetype:pptx

# Planilhas
site:exemplo.com filetype:xls
site:exemplo.com filetype:xlsx
```

### Pesquisa de Vazamentos
```bash
# HaveIBeenPwned
curl -s "https://haveibeenpwned.com/api/v3/breachedaccount/<EMAIL>"

# DeHashed
# Requer API key
curl -s "https://api.dehashed.com/search?q=<EMAIL>" \
  -H "Authorization: Basic $(echo -n 'username:api_key' | base64)"

# BreachDirectory
# Pesquisa local de vazamentos
```

## Automação e Scripts

### Script de Enumeração Completa
```bash
#!/bin/bash
# enum_completa.sh

DOMAIN=$1
OUTPUT_DIR="enum_$DOMAIN"

# Criar diretório de saída
mkdir -p $OUTPUT_DIR

echo "=== Enumeração de $DOMAIN ==="

# Whois
echo "Executando Whois..."
whois $DOMAIN > $OUTPUT_DIR/whois.txt

# DNS
echo "Executando consultas DNS..."
dig $DOMAIN A > $OUTPUT_DIR/dns_a.txt
dig $DOMAIN MX > $OUTPUT_DIR/dns_mx.txt
dig $DOMAIN NS > $OUTPUT_DIR/dns_ns.txt

# Subdomínios
echo "Enumerando subdomínios..."
theHarvester -d $DOMAIN -b all -f $OUTPUT_DIR/subdomains.txt

# Shodan
echo "Consultando Shodan..."
shodan search hostname:$DOMAIN > $OUTPUT_DIR/shodan.txt

echo "Enumeração concluída. Resultados em $OUTPUT_DIR/"
```

### Script de Monitoramento
```bash
#!/bin/bash
# monitor_domain.sh

DOMAIN=$1
PREVIOUS_FILE="previous_$DOMAIN.txt"
CURRENT_FILE="current_$DOMAIN.txt"

# Coletar informações atuais
theHarvester -d $DOMAIN -b all -f $CURRENT_FILE

# Comparar com versão anterior
if [ -f $PREVIOUS_FILE ]; then
    echo "Mudanças detectadas:"
    diff $PREVIOUS_FILE $CURRENT_FILE
fi

# Atualizar arquivo anterior
cp $CURRENT_FILE $PREVIOUS_FILE
```

## Relatórios e Documentação

### Template de Relatório
```markdown
# Relatório de Reconhecimento - OSINT

## Informações do Projeto
- **Alvo**: exemplo.com
- **Data**: 2024-01-15
- **Responsável**: Nome do Profissional
- **Escopo**: Reconhecimento passivo

## Descobertas

### Domínios e Subdomínios
- exemplo.com
- www.exemplo.com
- mail.exemplo.com
- admin.exemplo.com

### Endereços IP
- ***********
- ***********

### Funcionários Identificados
- João Silva (<EMAIL>)
- Maria Santos (<EMAIL>)

### Tecnologias Detectadas
- Apache 2.4
- PHP 7.4
- MySQL 5.7

## Recomendações
1. Implementar monitoramento de subdomínios
2. Revisar configurações de DNS
3. Implementar detecção de vazamentos

## Anexos
- Logs completos
- Capturas de tela
- Dados brutos
```

## Conclusão

As técnicas e ferramentas de OSINT fornecem:

- **Visibilidade Completa**: Compreensão abrangente do alvo
- **Eficiência**: Automação de processos repetitivos
- **Legalidade**: Técnicas legais e éticas
- **Escalabilidade**: Aplicação em múltiplos alvos

Para profissionais de segurança:
- **Fundamento**: Base para atividades ofensivas
- **Monitoramento**: Detecção de mudanças
- **Inteligência**: Coleta de informações estratégicas
- **Compliance**: Documentação de processos

O domínio destas técnicas é essencial para qualquer programa de segurança da informação. 