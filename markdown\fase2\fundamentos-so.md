# 🖥️ Fundamentos de Sistemas Operacionais

## Introdução aos Sistemas Operacionais

Os sistemas operacionais são o software fundamental que gerencia todos os recursos de um computador, fornecendo uma interface entre o hardware e os aplicativos. Em cibersegurança, entender como os sistemas operacionais funcionam é essencial para identificar vulnerabilidades, implementar controles de segurança e conduzir investigações forenses.

### O que é um Sistema Operacional?

Um sistema operacional (SO) é um software que atua como intermediário entre o usuário e o hardware do computador. Ele gerencia recursos como CPU, memória, armazenamento e dispositivos de entrada/saída, fornecendo uma interface padronizada para aplicativos.

**Principais funções de um SO:**
- **Gerenciamento de Processos**: Controla a execução de programas
- **Gerenciamento de Memória**: Aloca e gerencia memória RAM
- **Sistema de Arquivos**: Organiza e gerencia dados no armazenamento
- **Controle de Dispositivos**: Gerencia periféricos (impressoras, redes, etc.)
- **Interface do Usuário**: Fornece interface gráfica ou de linha de comando

## Linux: O Sistema Operacional da Cibersegurança

Linux é o sistema operacional mais utilizado em cibersegurança, tanto para ferramentas de ataque quanto para defesa. Sua arquitetura open-source, flexibilidade e robustez o tornam ideal para profissionais de segurança.

### Distribuições Linux para Segurança

**Kali Linux**: A distribuição mais popular para pentesting
- Mais de 600 ferramentas pré-instaladas
- Foco em testes de penetração e auditoria de segurança
- Atualizações regulares de ferramentas

**Parrot Security OS**: Alternativa ao Kali
- Interface mais amigável
- Ferramentas de desenvolvimento e análise forense
- Boa performance em máquinas com recursos limitados

**BlackArch**: Baseado no Arch Linux
- Mais de 2.500 ferramentas de segurança
- Para usuários avançados
- Instalação modular

### Estrutura de Diretórios Linux

```
/ (root)
├── /bin - Comandos essenciais do sistema
├── /boot - Arquivos de inicialização
├── /dev - Dispositivos de hardware
├── /etc - Arquivos de configuração
├── /home - Diretórios dos usuários
├── /lib - Bibliotecas do sistema
├── /media - Pontos de montagem para mídia removível
├── /mnt - Pontos de montagem temporários
├── /opt - Software opcional
├── /proc - Informações de processos (virtual)
├── /root - Diretório do superusuário
├── /sbin - Comandos de administração
├── /tmp - Arquivos temporários
├── /usr - Programas e dados de usuários
└── /var - Dados variáveis (logs, emails, etc.)
```

### Comandos Linux Essenciais

**Navegação e Arquivos:**
```bash
pwd          # Mostra diretório atual
ls           # Lista arquivos e diretórios
cd           # Muda diretório
mkdir        # Cria diretório
rm           # Remove arquivo
rmdir        # Remove diretório vazio
cp           # Copia arquivo
mv           # Move/renomeia arquivo
```

**Visualização de Conteúdo:**
```bash
cat          # Mostra conteúdo completo
less         # Visualiza arquivo página por página
head         # Mostra primeiras linhas
tail         # Mostra últimas linhas
grep         # Busca padrões em arquivos
```

**Permissões e Propriedade:**
```bash
chmod        # Altera permissões
chown        # Altera proprietário
chgrp        # Altera grupo
ls -la       # Lista com permissões detalhadas
```

**Sistema e Processos:**
```bash
ps           # Lista processos
top          # Monitor de processos em tempo real
kill         # Termina processo
systemctl    # Gerencia serviços do systemd
```

**Rede:**
```bash
ifconfig     # Configuração de interfaces de rede
ip           # Comando moderno para rede
netstat      # Estatísticas de rede
ss           # Versão moderna do netstat
ping         # Testa conectividade
nslookup     # Consulta DNS
```

### Permissões Linux

O sistema de permissões Linux usa três níveis:
- **Owner (Proprietário)**
- **Group (Grupo)**
- **Others (Outros)**

Cada nível tem três permissões:
- **r (read)**: Leitura
- **w (write)**: Escrita
- **x (execute)**: Execução

**Exemplo:**
```bash
-rw-r--r-- 1 <USER> <GROUP> 1234 Jan 1 12:00 arquivo.txt
```

**Decodificando:**
- `-`: Tipo de arquivo (- = arquivo, d = diretório)
- `rw-`: Permissões do proprietário (read, write, no execute)
- `r--`: Permissões do grupo (read only)
- `r--`: Permissões de outros (read only)

**Alterando permissões:**
```bash
chmod 755 arquivo    # rwxr-xr-x
chmod +x script.sh   # Adiciona permissão de execução
chmod -w arquivo     # Remove permissão de escrita
```

## Windows: O Sistema Dominante

Windows é o sistema operacional mais usado no mundo corporativo, tornando-o um alvo frequente para ataques. Entender Windows é crucial para segurança corporativa.

### Arquitetura Windows

**Componentes principais:**
- **Kernel**: Núcleo do sistema
- **Executive**: Serviços do sistema
- **Subsistemas**: Interface com aplicativos
- **Hardware Abstraction Layer (HAL)**: Interface com hardware

### Estrutura de Diretórios Windows

```
C:\
├── Windows\          # Sistema operacional
├── Program Files\    # Aplicativos 64-bit
├── Program Files (x86)\ # Aplicativos 32-bit
├── Users\            # Perfis de usuários
├── ProgramData\      # Dados compartilhados
└── System32\         # Bibliotecas do sistema
```

### Comandos Windows Essenciais

**Navegação e Arquivos:**
```cmd
dir              # Lista arquivos e diretórios
cd               # Muda diretório
mkdir            # Cria diretório
del              # Remove arquivo
copy             # Copia arquivo
move             # Move/renomeia arquivo
```

**Sistema e Processos:**
```cmd
tasklist         # Lista processos
taskkill         # Termina processo
services.msc     # Gerencia serviços
msconfig         # Configuração do sistema
```

**Rede:**
```cmd
ipconfig         # Configuração de rede
netstat          # Estatísticas de rede
ping             # Testa conectividade
nslookup         # Consulta DNS
tracert          # Rastreia rota
```

**PowerShell (mais moderno):**
```powershell
Get-Process       # Lista processos
Get-Service       # Lista serviços
Get-NetAdapter    # Interfaces de rede
Test-NetConnection # Testa conectividade
```

### Registro do Windows

O Registro é um banco de dados hierárquico que armazena configurações do sistema e aplicativos.

**Chaves principais:**
- **HKEY_LOCAL_MACHINE**: Configurações do sistema
- **HKEY_CURRENT_USER**: Configurações do usuário atual
- **HKEY_CLASSES_ROOT**: Associações de arquivos
- **HKEY_USERS**: Configurações de todos os usuários

**Comandos do Registro:**
```cmd
reg query         # Consulta valores
reg add          # Adiciona valores
reg delete       # Remove valores
reg export       # Exporta chave
reg import       # Importa chave
```

## Segurança em Sistemas Operacionais

### Vulnerabilidades Comuns

**Linux:**
- Configurações de permissões inadequadas
- Serviços desnecessários rodando
- Senhas fracas ou ausentes
- Kernel desatualizado

**Windows:**
- Contas de administrador mal configuradas
- Serviços desnecessários
- Configurações de segurança inadequadas
- Falta de patches de segurança

### Boas Práticas de Segurança

**Linux:**
```bash
# Desabilitar login root
sudo passwd -l root

# Configurar firewall
sudo ufw enable

# Atualizar sistema regularmente
sudo apt update && sudo apt upgrade

# Verificar permissões de arquivos sensíveis
ls -la /etc/passwd /etc/shadow
```

**Windows:**
```powershell
# Desabilitar conta de administrador
net user administrator /active:no

# Configurar firewall
netsh advfirewall set allprofiles state on

# Verificar atualizações
wuauclt /detectnow

# Auditar eventos de segurança
auditpol /set /category:"Logon/Logoff" /success:enable /failure:enable
```

### Ferramentas de Análise

**Linux:**
```bash
# Análise de processos
ps aux | grep suspicious

# Verificar conexões de rede
netstat -tuln

# Análise de logs
tail -f /var/log/auth.log

# Verificar integridade de arquivos
md5sum arquivo_importante
```

**Windows:**
```cmd
# Análise de processos
tasklist /v

# Verificar conexões de rede
netstat -an

# Análise de logs
wevtutil qe Security /c:10 /f:text

# Verificar integridade
certutil -hashfile arquivo_importante MD5
```

## Virtualização e Ambientes Isolados

### Tipos de Virtualização

**Virtualização de Sistema Completo:**
- VMware, VirtualBox, Hyper-V
- Isolamento completo
- Overhead significativo

**Containers:**
- Docker, LXC
- Isolamento de processos
- Baixo overhead

**Sandboxing:**
- Isolamento de aplicações
- Proteção contra malware
- Análise de comportamento

### Benefícios para Segurança

1. **Isolamento**: Previne propagação de malware
2. **Testes Seguros**: Permite testar software suspeito
3. **Ambientes Padronizados**: Reduz inconsistências
4. **Recuperação Rápida**: Snapshots permitem rollback
5. **Análise Forense**: Ambientes controlados para investigação

## Conclusão

O conhecimento de sistemas operacionais é fundamental para profissionais de cibersegurança. Linux e Windows têm arquiteturas diferentes, mas ambos requerem atenção especial para configurações de segurança, monitoramento de atividades e implementação de controles adequados.

A virtualização e os ambientes isolados são ferramentas essenciais para testes de segurança, análise de malware e desenvolvimento de habilidades práticas sem comprometer sistemas de produção.

---

**Próximos Passos:**
- Praticar comandos básicos em ambos os sistemas
- Configurar ambientes virtuais para testes
- Estudar logs de sistema para detecção de anomalias
- Aprender sobre hardening de sistemas operacionais


