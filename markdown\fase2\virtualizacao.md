# 1.4 - Virtualização

## Introdução

A virtualização é uma tecnologia que permite executar múltiplos sistemas operacionais e aplicações em um único hardware físico. Em cibersegurança, a virtualização oferece isolamento, controle de recursos e flexibilidade para ambientes de teste e produção.

## Objetivos de Aprendizado

- Compreender conceitos fundamentais de virtualização
- Diferenciar tipos de virtualização
- Entender máquinas virtuais e containers
- Aprender sobre Docker e ambientes isolados
- Reconhecer benefícios de segurança da virtualização

## Conceitos Fundamentais

### Virtualização

**Definição:** Técnica que permite criar versões virtuais de recursos computacionais, como sistemas operacionais, servidores, redes e armazenamento.

**Componentes básicos:**
- **Host:** Sistema físico que executa a virtualização
- **Guest:** Sistema virtual executado sobre o host
- **Hypervisor:** Software que gerencia as máquinas virtuais
- **VM (Virtual Machine):** Sistema operacional isolado

### Benefícios da Virtualização

**Eficiência:**
- Melhor utilização de recursos
- Redução de custos de hardware
- Consolidação de servidores

**Flexibilidade:**
- Criação rápida de ambientes
- Migração fácil entre hosts
- Backup e recuperação simplificados

**Segurança:**
- Isolamento entre sistemas
- Controle de acesso granular
- Ambientes de teste isolados

## Tipos de Virtualização

### Virtualização de Sistema Operacional

**Características:**
- Múltiplas instâncias do mesmo SO
- Compartilhamento do kernel
- Menor overhead que VMs completas
- Exemplo: Containers Docker

**Vantagens:**
- Baixo consumo de recursos
- Inicialização rápida
- Fácil distribuição

**Desvantagens:**
- Menos isolamento
- Compartilhamento do kernel
- Limitações de compatibilidade

### Virtualização de Hardware

**Características:**
- Sistemas operacionais completos
- Isolamento total
- Kernel próprio para cada VM
- Exemplo: VMware, VirtualBox

**Vantagens:**
- Isolamento completo
- Compatibilidade total
- Flexibilidade de SO

**Desvantagens:**
- Maior overhead
- Mais recursos necessários
- Inicialização mais lenta

### Virtualização de Aplicação

**Características:**
- Isolamento de aplicações
- Sem necessidade de SO completo
- Execução em ambiente controlado
- Exemplo: Java Virtual Machine

**Vantagens:**
- Portabilidade
- Isolamento de aplicação
- Controle de recursos

**Desvantagens:**
- Limitações de compatibilidade
- Overhead específico da aplicação

## Hypervisors

### Tipo 1 - Bare Metal

**Características:**
- Executa diretamente no hardware
- Sem sistema operacional host
- Melhor performance
- Exemplos: VMware ESXi, Microsoft Hyper-V

**Vantagens:**
- Performance superior
- Menor overhead
- Maior segurança

**Desvantagens:**
- Mais complexo de gerenciar
- Hardware específico necessário
- Menos flexibilidade

### Tipo 2 - Hosted

**Características:**
- Executa sobre sistema operacional
- Mais fácil de usar
- Menor performance
- Exemplos: VMware Workstation, VirtualBox

**Vantagens:**
- Fácil instalação e uso
- Compatibilidade ampla
- Flexibilidade de configuração

**Desvantagens:**
- Overhead adicional
- Performance limitada
- Dependência do SO host

## Máquinas Virtuais

### Componentes de uma VM

**Hardware Virtual:**
- CPU virtual
- Memória RAM
- Disco rígido
- Placa de rede
- Dispositivos USB

**Configuração:**
- Arquivo de configuração
- Disco virtual
- Snapshot (backup)
- Rede virtual

### Ciclo de Vida da VM

**Criação:**
1. Definir recursos (CPU, RAM, disco)
2. Instalar sistema operacional
3. Configurar rede
4. Instalar aplicações

**Operação:**
- Iniciar/parar conforme necessário
- Monitorar recursos
- Fazer backup regular
- Atualizar sistema

**Manutenção:**
- Aplicar patches
- Atualizar ferramentas
- Otimizar configuração
- Limpar arquivos temporários

### Ferramentas de Virtualização

**VMware:**
- VMware Workstation (desktop)
- VMware ESXi (servidor)
- VMware vSphere (enterprise)

**Microsoft:**
- Hyper-V (Windows)
- Azure (nuvem)

**Open Source:**
- VirtualBox (Oracle)
- KVM (Linux)
- Xen (Linux)

## Containers

### Conceito

**Container** é uma unidade de software que empacota código e dependências para execução isolada.

**Características:**
- Compartilhamento do kernel do host
- Isolamento de processos
- Inicialização rápida
- Baixo overhead

### Docker

**Componentes:**
- **Docker Engine:** Runtime para containers
- **Docker Image:** Template para containers
- **Docker Container:** Instância executando
- **Docker Hub:** Repositório de imagens

**Comandos básicos:**
```bash
# Listar imagens
docker images

# Executar container
docker run -d -p 80:80 nginx

# Listar containers
docker ps

# Parar container
docker stop container_id

# Remover container
docker rm container_id
```

### Dockerfile

**Exemplo de Dockerfile:**
```dockerfile
# Imagem base
FROM ubuntu:20.04

# Atualizar sistema
RUN apt-get update && apt-get install -y nginx

# Copiar arquivos
COPY index.html /var/www/html/

# Expor porta
EXPOSE 80

# Comando padrão
CMD ["nginx", "-g", "daemon off;"]
```

**Construir imagem:**
```bash
docker build -t minha-app .
```

### Orquestração de Containers

**Docker Compose:**
```yaml
version: '3'
services:
  web:
    image: nginx
    ports:
      - "80:80"
  db:
    image: mysql
    environment:
      MYSQL_ROOT_PASSWORD: secret
```

**Kubernetes:**
- Orquestração avançada
- Escalabilidade automática
- Balanceamento de carga
- Descoberta de serviços

## Segurança em Virtualização

### Isolamento

**Benefícios:**
- Falhas não se propagam
- Controle de acesso granular
- Ambientes de teste seguros
- Quarentena de sistemas comprometidos

**Implementação:**
- Redes virtuais separadas
- Controle de recursos
- Políticas de acesso
- Monitoramento isolado

### Vulnerabilidades Comuns

**Escape de VM:**
- Ataque que sai da VM para o host
- Exploração de vulnerabilidades do hypervisor
- Acesso não autorizado ao sistema host

**VM Hopping:**
- Ataque entre VMs no mesmo host
- Compartilhamento de recursos
- Vazamento de informações

**Resource Exhaustion:**
- Consumo excessivo de recursos
- Negação de serviço
- Impacto em outras VMs

### Controles de Segurança

**Hardening do Hypervisor:**
- Atualizações regulares
- Configuração segura
- Monitoramento de logs
- Controle de acesso

**Isolamento de Rede:**
- VLANs separadas
- Firewall entre VMs
- Segmentação de tráfego
- Monitoramento de rede

**Controle de Recursos:**
- Limites de CPU e RAM
- Quotas de disco
- Políticas de rede
- Monitoramento de uso

## Ambientes Isolados

### Sandbox

**Definição:** Ambiente isolado para execução de código não confiável.

**Aplicações:**
- Análise de malware
- Teste de software
- Execução de código suspeito
- Desenvolvimento seguro

**Implementação:**
- VMs dedicadas
- Containers isolados
- Sistemas de sandbox
- Emuladores

### Laboratórios Virtuais

**Uso em Cibersegurança:**
- Teste de vulnerabilidades
- Análise de malware
- Treinamento de segurança
- Pesquisa de segurança

**Configuração:**
- Rede isolada
- Ferramentas de análise
- Ambientes controlados
- Monitoramento completo

### Ambientes de Desenvolvimento

**Benefícios:**
- Consistência entre ambientes
- Fácil replicação
- Isolamento de dependências
- Controle de versões

**Ferramentas:**
- Vagrant (VMs)
- Docker (containers)
- Ansible (automação)
- Terraform (infraestrutura)

## Monitoramento e Logs

### Monitoramento de VMs

**Métricas importantes:**
- Uso de CPU e RAM
- I/O de disco
- Tráfego de rede
- Tempo de resposta

**Ferramentas:**
- VMware vCenter
- Hyper-V Manager
- Prometheus
- Grafana

### Logs de Virtualização

**Tipos de logs:**
- Logs do hypervisor
- Logs das VMs
- Logs de rede virtual
- Logs de segurança

**Análise:**
- Detecção de anomalias
- Investigação de incidentes
- Auditoria de acesso
- Compliance

## Backup e Recuperação

### Estratégias de Backup

**Backup Completo:**
- Cópia completa da VM
- Tempo de recuperação longo
- Maior espaço necessário

**Backup Incremental:**
- Apenas mudanças
- Recuperação mais rápida
- Menor espaço necessário

**Snapshot:**
- Estado instantâneo
- Recuperação muito rápida
- Uso temporário

### Recuperação de Desastres

**Planejamento:**
- Identificar VMs críticas
- Definir RTO e RPO
- Testar procedimentos
- Documentar processos

**Implementação:**
- Backup automático
- Replicação em tempo real
- Failover automático
- Monitoramento contínuo

## Ferramentas Práticas

### VMware vSphere

**Componentes:**
- ESXi (hypervisor)
- vCenter (gerenciamento)
- vMotion (migração)
- DRS (balanceamento)

**Comandos básicos:**
```bash
# Conectar ao ESXi
ssh root@esxi-host

# Listar VMs
vim-cmd vmsvc/getallvms

# Ligar VM
vim-cmd vmsvc/power.on vm_id

# Desligar VM
vim-cmd vmsvc/power.off vm_id
```

### Docker Avançado

**Redes Docker:**
```bash
# Criar rede
docker network create minha-rede

# Executar container na rede
docker run --network minha-rede nginx
```

**Volumes:**
```bash
# Criar volume
docker volume create meu-volume

# Montar volume
docker run -v meu-volume:/data nginx
```

**Docker Compose Avançado:**
```yaml
version: '3.8'
services:
  app:
    build: .
    environment:
      - NODE_ENV=production
    volumes:
      - ./logs:/app/logs
    networks:
      - app-network
    depends_on:
      - db
  db:
    image: postgres:13
    environment:
      POSTGRES_PASSWORD: secret
    volumes:
      - postgres_data:/var/lib/postgresql/data
volumes:
  postgres_data:
networks:
  app-network:
    driver: bridge
```

## Conclusão

A virtualização é uma tecnologia fundamental para cibersegurança moderna. Compreender seus conceitos permite:

1. **Isolar sistemas** - Criar ambientes seguros e controlados
2. **Testar segurança** - Usar VMs para análise de malware
3. **Gerenciar recursos** - Otimizar uso de hardware
4. **Recuperar rapidamente** - Implementar backup e DR
5. **Desenvolver com segurança** - Usar containers isolados

No próximo módulo, exploraremos os fundamentos de segurança da informação, complementando este conhecimento com princípios teóricos de proteção de dados. 