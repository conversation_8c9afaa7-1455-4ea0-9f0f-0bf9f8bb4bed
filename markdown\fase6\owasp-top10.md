# 6.2 - OWASP Top 10

## O que é o OWASP Top 10?
- **Definição**: Lista das 10 principais vulnerabilidades em aplicações web, mantida pela OWASP.
- **Objetivo**: Conscientizar sobre riscos críticos e promover boas práticas de desenvolvimento seguro.

## Principais Vulnerabilidades (2021)
1. **A01:2021 - Broken Access Control**
2. **A02:2021 - Cryptographic Failures**
3. **A03:2021 - Injection**
4. **A04:2021 - Insecure Design**
5. **A05:2021 - Security Misconfiguration**
6. **A06:2021 - Vulnerable and Outdated Components**
7. **A07:2021 - Identification and Authentication Failures**
8. **A08:2021 - Software and Data Integrity Failures**
9. **A09:2021 - Security Logging and Monitoring Failures**
10. **A10:2021 - Server-Side Request Forgery (SSRF)**

## Exemplos de Ataques
- **Injection**: SQL Injection, Command Injection
- **Broken Access Control**: Acesso não autorizado a recursos
- **XSS**: Execução de scripts maliciosos no navegador
- **Security Misconfiguration**: Serviços expostos, permissões excessivas

## Boas Práticas de Mitigação
- Validar e sanitizar entradas
- Implementar controle de acesso robusto
- Manter componentes atualizados
- Configurar corretamente servidores e aplicações
- Monitorar e registrar eventos de segurança

## Conclusão
Conhecer o OWASP Top 10 é fundamental para identificar, priorizar e corrigir riscos em aplicações web. 