# 3.3 - Controle de Versões com Git

## Fundamentos de Controle de Versão

### O que é Controle de Versão
- **Definição**: Sistema que gerencia mudanças em arquivos ao longo do tempo
- **Objetivos**:
  - Rastrear histórico de mudanças
  - Permitir colaboração entre desenvolvedores
  - Reverter mudanças quando necessário
  - Manter diferentes versões do projeto

### Tipos de Controle de Versão

#### Controle de Versão Local
- **Características**: Versões armazenadas localmente
- **Exemplo**: RCS (Revision Control System)
- **Limitações**: Sem colaboração, risco de perda de dados

#### Controle de Versão Centralizado
- **Características**: Servidor central com histórico completo
- **Exemplo**: SVN (Subversion)
- **Vantagens**: Controle centralizado, permissões granulares
- **Desvantagens**: Ponto único de falha, dependência de rede

#### Controle de Versão Distribuído
- **Características**: Cada cliente tem cópia completa do repositório
- **Exemplo**: Git, Mercurial
- **Vantagens**: Trabalho offline, redundância, flexibilidade
- **Desvantagens**: Curva de aprendizado, complexidade

## Introdução ao Git

### Características do Git
- **Distribuído**: Cada clone é um repositório completo
- **Rápido**: Operações locais são muito rápidas
- **Flexível**: Suporta diferentes fluxos de trabalho
- **Seguro**: Integridade de dados garantida por SHA-1

### Conceitos Fundamentais

#### Repositório
- **Working Directory**: Diretório de trabalho com arquivos
- **Staging Area**: Área de preparação para commit
- **Repository**: Histórico de commits

#### Estados dos Arquivos
1. **Untracked**: Arquivo não rastreado pelo Git
2. **Modified**: Arquivo modificado mas não preparado
3. **Staged**: Arquivo preparado para commit
4. **Committed**: Arquivo salvo no repositório

## Configuração Inicial

### Instalação e Configuração
```bash
# Verificar se Git está instalado
git --version

# Configurar usuário global
git config --global user.name "Seu Nome"
git config --global user.email "<EMAIL>"

# Configurar editor padrão
git config --global core.editor "code --wait"

# Verificar configurações
git config --list
```

### Configurações Importantes
```bash
# Configurar branch padrão
git config --global init.defaultBranch main

# Configurar autenticação
git config --global credential.helper store

# Configurar aliases úteis
git config --global alias.st status
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
```

## Comandos Básicos

### Inicialização e Clonagem
```bash
# Inicializar repositório local
git init

# Clonar repositório remoto
git clone https://github.com/usuario/repositorio.git

# Clonar com nome específico
git clone https://github.com/usuario/repositorio.git meu-projeto
```

### Verificação de Status
```bash
# Ver status dos arquivos
git status

# Ver histórico de commits
git log

# Ver histórico resumido
git log --oneline

# Ver diferenças
git diff
git diff --staged
```

### Adicionando e Comitando
```bash
# Adicionar arquivo específico
git add arquivo.txt

# Adicionar todos os arquivos
git add .

# Adicionar arquivos modificados
git add -u

# Fazer commit
git commit -m "Mensagem do commit"

# Adicionar e commitar em uma operação
git commit -am "Mensagem do commit"
```

## Branches e Merge

### Trabalhando com Branches
```bash
# Ver branches
git branch

# Criar nova branch
git branch nova-feature

# Mudar para branch
git checkout nova-feature

# Criar e mudar para branch
git checkout -b nova-feature

# Deletar branch
git branch -d nova-feature
git branch -D nova-feature  # Forçar deleção
```

### Merge e Rebase
```bash
# Fazer merge de branch
git checkout main
git merge nova-feature

# Fazer rebase
git checkout nova-feature
git rebase main

# Resolver conflitos
# Editar arquivos com conflitos
git add arquivos-resolvidos
git rebase --continue
```

## Repositórios Remotos

### Configurando Remotes
```bash
# Adicionar remote
git remote add origin https://github.com/usuario/repositorio.git

# Ver remotes
git remote -v

# Mudar URL do remote
git remote set-url origin https://github.com/usuario/novo-repositorio.git

# Remover remote
git remote remove origin
```

### Push e Pull
```bash
# Enviar commits para remote
git push origin main

# Baixar mudanças do remote
git pull origin main

# Fazer fetch (só baixar, não mergear)
git fetch origin

# Ver diferenças antes do pull
git fetch origin
git log HEAD..origin/main
```

## Fluxos de Trabalho

### Git Flow
```bash
# Estrutura de branches
main          # Produção
develop       # Desenvolvimento
feature/*     # Novas funcionalidades
release/*     # Preparação para release
hotfix/*      # Correções urgentes

# Criar feature branch
git checkout develop
git checkout -b feature/nova-funcionalidade

# Finalizar feature
git checkout develop
git merge feature/nova-funcionalidade
git branch -d feature/nova-funcionalidade
```

### GitHub Flow
```bash
# Fluxo simplificado
main          # Sempre deployável
feature/*     # Branches para features

# Workflow
git checkout -b feature/nova-funcionalidade
# Fazer mudanças
git commit -am "Adicionar nova funcionalidade"
git push origin feature/nova-funcionalidade
# Criar Pull Request
# Code review
# Merge para main
```

## Aplicações em Segurança

### Versionamento de Scripts de Segurança
```bash
# Estrutura de projeto
projeto-seguranca/
├── scripts/
│   ├── monitoramento/
│   ├── hardening/
│   └── analise/
├── configs/
├── docs/
└── README.md

# Commit de script de segurança
git add scripts/monitoramento/scan_ports.py
git commit -m "feat: adicionar scanner de portas

- Implementar scanner básico de portas
- Adicionar suporte a múltiplas threads
- Incluir logging de resultados"
```

### Controle de Configurações
```bash
# Versionar configurações de segurança
git add configs/firewall_rules.conf
git commit -m "config: atualizar regras de firewall

- Adicionar regras para novos serviços
- Bloquear portas desnecessárias
- Melhorar logging de tráfego"
```

### Documentação de Incidentes
```bash
# Documentar incidente
git add docs/incidentes/2024-01-15-phishing.md
git commit -m "docs: documentar incidente de phishing

- Detalhar vetor de ataque
- Listar ações tomadas
- Documentar lições aprendidas"
```

## Boas Práticas

### Mensagens de Commit
```bash
# Formato convencional
<tipo>(<escopo>): <descrição>

[corpo opcional]

[rodapé opcional]

# Exemplos
feat: adicionar validação de senha
fix(script): corrigir erro de parsing
docs: atualizar README
style: formatar código
refactor: reorganizar estrutura
test: adicionar testes unitários
```

### .gitignore para Projetos de Segurança
```bash
# Arquivos sensíveis
*.key
*.pem
*.p12
*.pfx
passwords.txt
secrets.conf

# Logs
*.log
logs/

# Dados temporários
*.tmp
*.temp
temp/

# Arquivos de sistema
.DS_Store
Thumbs.db

# IDEs
.vscode/
.idea/
*.swp
*.swo
```

### Segurança em Repositórios
```bash
# Verificar arquivos sensíveis antes do commit
git diff --cached

# Verificar histórico por credenciais
git log -S "password" --all
git log -S "api_key" --all

# Remover arquivo sensível do histórico
git filter-branch --force --index-filter \
  'git rm --cached --ignore-unmatch arquivo-sensivel.txt' \
  --prune-empty --tag-name-filter cat -- --all
```

## Ferramentas e Integrações

### Git Hooks
```bash
# Pre-commit hook para validação
#!/bin/bash
# .git/hooks/pre-commit

# Verificar se há credenciais hardcoded
if git diff --cached | grep -i "password\|secret\|key"; then
    echo "ERRO: Possível credencial hardcoded detectada!"
    exit 1
fi

# Executar testes
python -m pytest tests/
if [ $? -ne 0 ]; then
    echo "ERRO: Testes falharam!"
    exit 1
fi
```

### Integração com CI/CD
```yaml
# .github/workflows/security.yml
name: Security Checks

on: [push, pull_request]

jobs:
  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Run security scan
      run: |
        pip install bandit
        bandit -r .
    
    - name: Run dependency check
      run: |
        pip install safety
        safety check
```

## Comandos Avançados

### Stash e Reset
```bash
# Salvar mudanças temporariamente
git stash
git stash push -m "Trabalho em progresso"

# Aplicar stash
git stash pop
git stash apply stash@{0}

# Reset de commits
git reset --soft HEAD~1    # Mantém mudanças no staging
git reset --mixed HEAD~1   # Remove do staging
git reset --hard HEAD~1    # Remove completamente
```

### Cherry-pick e Rebase
```bash
# Aplicar commit específico
git cherry-pick abc1234

# Rebase interativo
git rebase -i HEAD~3

# Squash commits
# No editor: s (squash) para commits que quer combinar
```

### Tags e Releases
```bash
# Criar tag
git tag v1.0.0
git tag -a v1.0.0 -m "Release 1.0.0"

# Push de tags
git push origin v1.0.0
git push origin --tags

# Ver tags
git tag
git show v1.0.0
```

## Troubleshooting

### Problemas Comuns
```bash
# Commit no branch errado
git reset --soft HEAD~1
git checkout branch-correto
git commit -c ORIG_HEAD

# Commit com mensagem errada
git commit --amend -m "Nova mensagem"

# Arquivo deletado acidentalmente
git checkout HEAD -- arquivo-deletado

# Reset de arquivo específico
git checkout HEAD -- arquivo-modificado
```

### Recuperação de Dados
```bash
# Ver commits perdidos
git reflog

# Recuperar commit perdido
git checkout -b recovery abc1234

# Recuperar branch deletado
git checkout -b branch-recuperado abc1234
```

## Conclusão

O Git fornece:

- **Controle de Versão Robusto**: Histórico completo de mudanças
- **Colaboração Eficiente**: Trabalho em equipe sem conflitos
- **Flexibilidade**: Diferentes fluxos de trabalho
- **Segurança**: Integridade de dados garantida

Para projetos de segurança:
- **Rastreabilidade**: Histórico de mudanças em scripts e configurações
- **Colaboração**: Trabalho em equipe em ferramentas de segurança
- **Auditoria**: Logs de quem fez o quê e quando
- **Recuperação**: Possibilidade de reverter mudanças problemáticas

O domínio do Git é essencial para qualquer profissional de segurança que trabalha com código, scripts ou configurações. 