# 5.1 - Conceitos de Vulnerabilidades

## O que é uma Vulnerabilidade?
- **Definição**: Fraqueza em um sistema que pode ser explorada para comprometer a segurança.
- **Exemplos**: Bugs de software, configurações incorretas, falhas de autenticação.

## Ciclo de Vida da Vulnerabilidade
1. **Descoberta**: Identificação por pesquisadores, usuários ou atacantes.
2. **Divulgação**: Comunicação ao fornecedor ou ao público.
3. **Classificação**: Avaliação de severidade (CVE, CVSS, CWE).
4. **Correção**: Desenvolvimento e aplicação de patch.
5. **Exploração**: Uso por atacantes caso não corrigida.

## Principais Padrões e Classificações

### CVE (Common Vulnerabilities and Exposures)
- **Identificador único** para vulnerabilidades conhecidas.
- Exemplo: CVE-2023-12345

### CVSS (Common Vulnerability Scoring System)
- **Sistema de pontuação** de severidade (0 a 10).
- Métricas: Exploitability, Impact, Scope.
- Exemplo: CVSS 9.8 (Crítico)

### CWE (Common Weakness Enumeration)
- **Catálogo de tipos de fraquezas** em software.
- Exemplo: CWE-79 (Cross-Site Scripting)

## Tipos Comuns de Vulnerabilidades
- **Buffer Overflow**: Escrita além do limite de memória.
- **Injeção de SQL**: Inserção de comandos maliciosos em consultas.
- **Cross-Site Scripting (XSS)**: Execução de scripts no navegador da vítima.
- **Deserialização Insegura**: Execução de código ao desserializar dados não confiáveis.
- **Configuração Incorreta**: Serviços expostos, permissões excessivas.

## Gestão de Patches
- **Identificação**: Monitorar fontes de vulnerabilidades (NVD, advisories).
- **Avaliação**: Priorizar patches críticos.
- **Aplicação**: Testar e aplicar patches rapidamente.
- **Verificação**: Validar se a vulnerabilidade foi corrigida.

## Boas Práticas
- Manter sistemas e softwares atualizados.
- Realizar varreduras periódicas de vulnerabilidades.
- Implementar defesa em profundidade.
- Treinar equipes para resposta rápida.

## Conclusão
A compreensão dos conceitos de vulnerabilidades é fundamental para proteger sistemas e priorizar ações de correção. 