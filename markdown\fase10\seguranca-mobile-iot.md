# 10.4 - Segurança Mobile e IoT

## Segurança Mobile

### Android
- **Arquitetura**: Sistema baseado em Linux com sandboxing.
- **Permissões**: Sistema de permissões granulares.
- **APK Analysis**: Análise de arquivos APK para malware.
- **Root Detection**: Detecção de dispositivos com root.

### iOS
- **Arquitetura**: Sistema fechado com sandboxing rigoroso.
- **App Store**: Distribuição controlada de aplicações.
- **Jailbreak Detection**: Detecção de dispositivos com jailbreak.
- **Code Signing**: Assinatura digital obrigatória.

## Internet of Things (IoT)

### O que é IoT?
- **Definição**: Rede de dispositivos físicos conectados à internet.
- **Exemplos**: Smartphones, smart TVs, câmeras IP, termostatos.

### Vulnerabilidades Comuns
- **Weak Authentication**: <PERSON><PERSON> ou fracas.
- **Unencrypted Communication**: Comunicação não criptografada.
- **Lack of Updates**: Falta de atualizações de segurança.
- **Default Configurations**: Configurações padrão inseguras.

## Análise de APKs

### Estrutura de APK
- **Manifest**: Arquivo de configuração da aplicação.
- **DEX Files**: Código compilado da aplicação.
- **Resources**: Recursos da aplicação (imagens, strings).
- **Native Libraries**: Bibliotecas nativas.

### Técnicas de Análise
- **Static Analysis**: Análise do código sem execução.
- **Dynamic Analysis**: Análise durante execução.
- **Reverse Engineering**: Engenharia reversa do código.

## Sandboxing

### O que é Sandboxing?
- **Definição**: Isolamento de aplicações para limitar acesso a recursos.
- **Objetivo**: Prevenir que aplicações maliciosas afetem o sistema.

### Implementações
- **Android**: Cada aplicação roda em seu próprio processo.
- **iOS**: Sandboxing rigoroso com permissões limitadas.
- **Web Browsers**: Isolamento de abas e processos.

## Boas Práticas

### Para Desenvolvedores
- Implementar autenticação forte.
- Criptografar dados sensíveis.
- Validar entradas do usuário.
- Atualizar dependências regularmente.

### Para Usuários
- Manter dispositivos atualizados.
- Usar apenas aplicações confiáveis.
- Configurar autenticação multifator.
- Monitorar permissões de aplicações.

## Conclusão
A segurança mobile e IoT é crítica devido ao grande número de dispositivos conectados e à quantidade de dados sensíveis processados. 