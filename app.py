from flask import Flask, render_template, jsonify
import os
from datetime import datetime
import markdown
from pathlib import Path

app = Flask(__name__)

# Configuração para servir arquivos estáticos
app.static_folder = 'static'
app.template_folder = 'templates'



@app.route('/')
def home():
    """Página inicial do CYPHER"""
    return render_template('index.html')

@app.route('/estudos')
def estudos():
    """Página principal de estudos com dashboard"""
    return render_template('estudos.html')

@app.route('/markdown/<path:filepath>')
def get_markdown_content(filepath):
    """Serve conteúdo markdown convertido para HTML"""
    try:
        # Construir caminho seguro para o arquivo
        markdown_path = Path('markdown') / filepath

        # Verificar se o arquivo existe e está dentro da pasta markdown
        if not markdown_path.exists() or not str(markdown_path).startswith('markdown'):
            return jsonify({'error': 'Arquivo não encontrado'}), 404

        # Ler e converter markdown para HTML
        with open(markdown_path, 'r', encoding='utf-8') as file:
            markdown_content = file.read()

        # Converter markdown para HTML com configurações melhoradas
        html_content = markdown.markdown(
            markdown_content,
            extensions=[
                'codehilite',
                'fenced_code', 
                'tables', 
                'toc',
                'nl2br',  # Quebras de linha automáticas
                'sane_lists'  # Listas mais consistentes
            ],
            output_format='html5'
        )

        return jsonify({
            'content': html_content,
            'filepath': str(filepath)
        })

    except Exception as e:
        return jsonify({'error': f'Erro ao carregar arquivo: {str(e)}'}), 500



@app.route('/api/time')
def get_current_time():
    """API para obter horário atual"""
    return jsonify({
        'time': datetime.now().strftime('%H:%M:%S'),
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/connection-status')
def get_connection_status():
    """API para simular status de conexão"""
    import random
    return jsonify({
        'connected': random.choice([True, False]),
        'status': 'CONNECTED' if random.choice([True, False]) else 'RECONNECTING...'
    })

# Dados do currículo de estudos




if __name__ == '__main__':
    # Criar diretórios se não existirem
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)
    os.makedirs('static/images', exist_ok=True)
    os.makedirs('templates', exist_ok=True)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
